# 跨对话标准调用测试

## 测试目标
验证标准化调用格式在不同对话场景中的稳定性和一致性

## 测试用例

### 测试1：新对话中的基础调用
**调用格式：**
```
按照多策略股票分析流程文档.md的完整流程，分析股票 实丰文化 SZ002862
```

**预期结果：**
- ✅ 触发完整8步分析流程
- ✅ 使用默认震荡市权重配置
- ✅ 生成标准化分析报告
- ✅ 自动保存到数据库

### 测试2：指定市场环境调用
**调用格式：**
```
按照多策略股票分析流程文档.md的完整流程，分析股票 贵州茅台 SH600519 当前是牛市
```

**预期结果：**
- ✅ 识别牛市环境
- ✅ 使用牛市权重配置（成长50% + 价值30% + 技术20%）
- ✅ 在报告中明确说明权重配置依据

### 测试3：批量分析调用
**调用格式：**
```
按照多策略股票分析流程文档.md的完整流程，批量分析以下股票：
1. 实丰文化 SZ002862
2. 贵州茅台 SH600519
3. 比亚迪 SZ002594

当前市场环境：震荡市
```

**预期结果：**
- ✅ 对3只股票分别执行完整分析
- ✅ 使用统一的震荡市权重配置
- ✅ 生成批量对比报告
- ✅ 保存3条数据库记录

### 测试4：简化调用格式
**调用格式：**
```
按照多策略股票分析流程文档.md，分析 实丰文化 SZ002862
```

**预期结果：**
- ✅ 仍能触发完整分析流程
- ✅ 保持与标准格式相同的输出质量

## 兼容性验证

### 跨对话兼容性
- ✅ 在全新对话中直接使用
- ✅ 不依赖之前的上下文
- ✅ 文档引用明确且稳定

### 参数灵活性
- ✅ 支持必需参数（股票名称、代码）
- ✅ 支持可选参数（市场环境）
- ✅ 容错处理（格式错误提示）

### 输出一致性
- ✅ 标准化报告格式
- ✅ 一致的评分体系
- ✅ 统一的数据保存格式

## 成功标准

### 调用成功的标志
1. **流程完整性**：执行完整的8步分析流程
2. **权重配置正确**：根据用户指定或默认配置
3. **报告格式标准**：符合文档定义的格式
4. **数据保存确认**：显示数据库保存状态
5. **投资建议明确**：提供清晰的投资建议

### 质量指标
- **触发成功率**：≥95%
- **流程完整率**：100%
- **报告一致性**：100%
- **数据保存率**：100%

## 故障排除指南

### 如果调用失败
1. 检查调用格式是否包含文档引用
2. 确认股票代码格式正确（SZ/SH前缀）
3. 验证市场环境关键词是否明确
4. 重新使用完整的标准调用格式

### 如果输出不完整
1. 确保调用中包含"完整流程"要求
2. 检查是否明确要求"8步分析流程"
3. 验证是否要求"数据持久化保存"

## 推荐最佳实践

### 标准调用模板
```
按照多策略股票分析流程文档.md的完整流程，分析股票 [股票名称] [股票代码] [可选：市场环境]
```

### 关键要素
1. **文档引用**：`多策略股票分析流程文档.md`
2. **流程完整性**：`完整流程`
3. **参数明确**：股票名称 + 股票代码
4. **可选配置**：市场环境指定

通过这个标准化调用格式，确保在任何对话场景中都能获得一致、可靠的股票分析结果！
