#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM股票分析工具使用示例
演示如何在LLM工作流程中使用股票分析数据存储工具
"""

from llm_stock_tools import (
    save_stock_analysis,
    save_batch_stock_analysis,
    get_recent_analysis,
    get_stock_history,
    quick_save_analysis
)

def example_single_save():
    """示例1：保存单条股票分析结果"""
    print("📊 示例1：保存单条股票分析结果")
    print("=" * 50)
    
    # 模拟LLM分析完成后的结果
    result = save_stock_analysis(
        stock_name="实丰文化",
        stock_code="SZ002862",
        value_score=45.0,
        growth_score=62.0,
        technical_score=48.0,
        total_score=52.3,
        confidence_level="中",
        weight_config="系统默认",
        market_environment="震荡市",
        detailed_analysis="价值策略45分：估值偏高但财务结构尚可；成长策略62分：2024年业绩显著改善；技术策略48分：短期技术面一般",
        investment_advice="谨慎考虑 - 业绩改善明显但估值仍然偏高，适合风险偏好较高的成长型投资者",
        risk_warnings="估值风险：PE=241.04存在较大回调风险；业绩风险：盈利改善的可持续性存疑"
    )
    
    if result['success']:
        print(f"✅ {result['message']}")
    else:
        print(f"❌ {result['message']}")
    
    return result

def example_batch_save():
    """示例2：批量保存股票分析结果"""
    print("\n📊 示例2：批量保存股票分析结果")
    print("=" * 50)
    
    # 模拟LLM批量分析的结果
    analysis_list = [
        {
            'stock_name': '贵州茅台',
            'stock_code': 'SH600519',
            'value_score': 85.0,
            'growth_score': 75.0,
            'technical_score': 70.0,
            'total_score': 78.5,
            'confidence_level': '高',
            'weight_config': '用户指定',
            'market_environment': '牛市',
            'detailed_analysis': '价值策略85分：估值合理且财务质量优秀；成长策略75分：稳定增长且品牌价值突出；技术策略70分：长期趋势向上',
            'investment_advice': '强烈推荐 - 白酒龙头企业，具备长期投资价值，适合价值投资者长期持有',
            'risk_warnings': '市场风险：短期可能受宏观经济影响；估值风险：当前估值不便宜'
        },
        {
            'stock_name': '比亚迪',
            'stock_code': 'SZ002594',
            'value_score': 60.0,
            'growth_score': 88.0,
            'technical_score': 65.0,
            'total_score': 72.8,
            'confidence_level': '高',
            'weight_config': '用户指定',
            'market_environment': '牛市',
            'detailed_analysis': '价值策略60分：估值偏高但基本面良好；成长策略88分：新能源汽车领域增长强劲；技术策略65分：技术面表现良好',
            'investment_advice': '推荐 - 新能源汽车龙头，成长性突出，适合成长型投资者',
            'risk_warnings': '行业风险：新能源政策变化风险；竞争风险：行业竞争加剧'
        }
    ]
    
    result = save_batch_stock_analysis(analysis_list)
    
    print(f"📈 {result['message']}")
    if result['failed_count'] > 0:
        print("❌ 失败项目:")
        for failed_item in result['failed_items']:
            print(f"  - 第{failed_item['index']+1}项: {failed_item['error']}")
    
    return result

def example_quick_save():
    """示例3：使用便捷函数保存（支持中文字段名）"""
    print("\n📊 示例3：使用便捷函数保存（支持中文字段名）")
    print("=" * 50)
    
    # 模拟LLM使用中文字段名的分析结果
    analysis_result = {
        "股票名称": "中国平安",
        "股票代码": "SH601318",
        "价值策略评分": 75.0,
        "成长策略评分": 55.0,
        "技术策略评分": 60.0,
        "综合评分": 64.5,
        "置信度": "中",
        "权重配置": "系统默认",
        "市场环境": "震荡市",
        "详细分析": "价值策略75分：估值较低且分红稳定；成长策略55分：增长放缓但基础稳固；技术策略60分：技术面中性",
        "投资建议": "谨慎考虑 - 金融龙头企业，估值合理但增长有限，适合稳健型投资者",
        "风险提示": "监管风险：金融监管政策变化；市场风险：利率环境变化影响"
    }
    
    result = quick_save_analysis(analysis_result)
    
    if result['success']:
        print(f"✅ {result['message']}")
    else:
        print(f"❌ {result['message']}")
    
    return result

def example_query_recent():
    """示例4：查询最近的分析记录"""
    print("\n📊 示例4：查询最近的分析记录")
    print("=" * 50)
    
    result = get_recent_analysis(limit=5)
    
    if result['success']:
        print(f"📋 {result['message']}")
        print("-" * 80)
        print(f"{'股票名称':<10} {'代码':<10} {'总分':<8} {'置信度':<8} {'分析时间':<20}")
        print("-" * 80)
        
        for record in result['data']:
            print(f"{record['stock_name']:<10} {record['stock_code']:<10} {record['total_score']:<8.1f} {record['confidence_level']:<8} {record['analysis_datetime']:<20}")
    else:
        print(f"❌ {result['message']}")
    
    return result

def example_query_history():
    """示例5：查询指定股票的历史记录"""
    print("\n📊 示例5：查询指定股票的历史记录")
    print("=" * 50)
    
    stock_code = "SZ002862"
    result = get_stock_history(stock_code, limit=3)
    
    if result['success']:
        print(f"📈 {result['message']}")
        if result['count'] > 0:
            print("-" * 60)
            for i, record in enumerate(result['data'], 1):
                print(f"{i}. {record['analysis_datetime']}")
                print(f"   总分: {record['total_score']}分 | 置信度: {record['confidence_level']}")
                print(f"   价值: {record['value_score']}分 | 成长: {record['growth_score']}分 | 技术: {record['technical_score']}分")
                if i < len(result['data']):
                    print()
        else:
            print("📭 暂无历史记录")
    else:
        print(f"❌ {result['message']}")
    
    return result

def simulate_llm_workflow():
    """模拟LLM完整工作流程"""
    print("\n🤖 模拟LLM完整工作流程")
    print("=" * 50)
    
    # 步骤1：LLM执行股票分析（这里用模拟数据）
    print("1️⃣ LLM执行股票分析...")
    
    # 步骤2：LLM调用工具保存分析结果
    print("2️⃣ 保存分析结果到数据库...")
    
    analysis_data = {
        "股票名称": "宁德时代",
        "股票代码": "SZ300750",
        "价值策略评分": 55.0,
        "成长策略评分": 82.0,
        "技术策略评分": 58.0,
        "综合评分": 66.2,
        "置信度": "中",
        "权重配置": "系统默认",
        "市场环境": "震荡市",
        "详细分析": "价值策略55分：估值偏高但行业地位突出；成长策略82分：电池技术领先且市场份额稳固；技术策略58分：短期调整中",
        "投资建议": "谨慎考虑 - 动力电池龙头，技术优势明显但估值偏高，适合长期投资者",
        "风险提示": "估值风险：当前估值较高；技术风险：电池技术迭代风险"
    }
    
    save_result = quick_save_analysis(analysis_data)
    
    if save_result['success']:
        print(f"✅ 数据保存成功: {save_result['message']}")
        
        # 步骤3：验证保存结果
        print("3️⃣ 验证保存结果...")
        recent_result = get_recent_analysis(limit=1)
        
        if recent_result['success'] and recent_result['count'] > 0:
            latest_record = recent_result['data'][0]
            print(f"✅ 验证成功: 最新记录为 {latest_record['stock_name']} ({latest_record['stock_code']})")
            print(f"   综合评分: {latest_record['total_score']}分")
            print(f"   置信度: {latest_record['confidence_level']}")
        else:
            print("❌ 验证失败")
    else:
        print(f"❌ 数据保存失败: {save_result['message']}")

def main():
    """主函数：运行所有示例"""
    print("🚀 LLM股票分析工具使用示例")
    print("=" * 60)
    
    try:
        # 运行各个示例
        example_single_save()
        example_batch_save()
        example_quick_save()
        example_query_recent()
        example_query_history()
        simulate_llm_workflow()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")

if __name__ == "__main__":
    main()
