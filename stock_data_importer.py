#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能股票分析系统v2.0 - 数据导入工具
简化向SQLite数据库添加股票分析数据的操作

使用方法：
1. 交互式添加：python stock_data_importer.py
2. 批量导入：python stock_data_importer.py --csv data.csv
3. 查看帮助：python stock_data_importer.py --help
"""

import sqlite3
import csv
import argparse
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class StockDataImporter:
    """股票分析数据导入器"""
    
    def __init__(self, db_path: str = "stock_analysis.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    analysis_datetime TEXT NOT NULL,
                    stock_name TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    value_score REAL NOT NULL,
                    growth_score REAL NOT NULL,
                    technical_score REAL NOT NULL,
                    total_score REAL NOT NULL,
                    confidence_level TEXT NOT NULL,
                    weight_config TEXT NOT NULL,
                    market_environment TEXT NOT NULL,
                    detailed_analysis TEXT,
                    investment_advice TEXT,
                    risk_warnings TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_code ON stock_analysis_results(stock_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_datetime ON stock_analysis_results(analysis_datetime)')
            
            conn.commit()
            print(f"✅ 数据库初始化成功: {self.db_path}")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def validate_stock_code(self, stock_code: str) -> bool:
        """验证股票代码格式"""
        pattern = r'^(SZ|SH)\d{6}$'
        return bool(re.match(pattern, stock_code.upper()))
    
    def validate_score(self, score: float, field_name: str) -> bool:
        """验证评分范围"""
        if not isinstance(score, (int, float)):
            print(f"❌ {field_name}必须是数字")
            return False
        if not 0 <= score <= 100:
            print(f"❌ {field_name}必须在0-100之间")
            return False
        return True
    
    def validate_confidence_level(self, level: str) -> bool:
        """验证置信度等级"""
        valid_levels = ['高', '中', '低']
        if level not in valid_levels:
            print(f"❌ 置信度等级必须是: {', '.join(valid_levels)}")
            return False
        return True
    
    def validate_market_environment(self, env: str) -> bool:
        """验证市场环境"""
        valid_envs = ['牛市', '熊市', '震荡市', '默认']
        if env not in valid_envs:
            print(f"❌ 市场环境必须是: {', '.join(valid_envs)}")
            return False
        return True
    
    def validate_data(self, data: Dict) -> Tuple[bool, str]:
        """验证数据完整性和格式"""
        required_fields = [
            'stock_name', 'stock_code', 'value_score', 'growth_score',
            'technical_score', 'total_score', 'confidence_level',
            'weight_config', 'market_environment'
        ]
        
        # 检查必需字段
        for field in required_fields:
            if field not in data or data[field] is None or data[field] == '':
                return False, f"缺少必需字段: {field}"
        
        # 验证股票代码格式
        if not self.validate_stock_code(data['stock_code']):
            return False, f"股票代码格式错误: {data['stock_code']} (应为SZ000001或SH600000格式)"
        
        # 验证评分范围
        score_fields = ['value_score', 'growth_score', 'technical_score', 'total_score']
        for field in score_fields:
            try:
                score = float(data[field])
                if not self.validate_score(score, field):
                    return False, f"评分验证失败: {field}"
                data[field] = score  # 转换为float
            except ValueError:
                return False, f"评分必须是数字: {field}"
        
        # 验证置信度等级
        if not self.validate_confidence_level(data['confidence_level']):
            return False, "置信度等级验证失败"
        
        # 验证市场环境
        if not self.validate_market_environment(data['market_environment']):
            return False, "市场环境验证失败"
        
        return True, "验证通过"
    
    def check_duplicate(self, stock_code: str, analysis_datetime: str) -> bool:
        """检查重复数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT COUNT(*) FROM stock_analysis_results 
                WHERE stock_code = ? AND analysis_datetime = ?
            ''', (stock_code, analysis_datetime))
            
            count = cursor.fetchone()[0]
            return count > 0
            
        except Exception as e:
            print(f"⚠️ 重复检查失败: {e}")
            return False
        finally:
            if conn:
                conn.close()
    
    def add_single_record(self, data: Dict) -> bool:
        """添加单条记录"""
        # 添加分析时间（如果未提供）
        if 'analysis_datetime' not in data or not data['analysis_datetime']:
            data['analysis_datetime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 验证数据
        is_valid, message = self.validate_data(data)
        if not is_valid:
            print(f"❌ 数据验证失败: {message}")
            return False
        
        # 检查重复
        if self.check_duplicate(data['stock_code'], data['analysis_datetime']):
            print(f"⚠️ 发现重复数据: {data['stock_code']} {data['analysis_datetime']}")
            response = input("是否覆盖? (y/N): ").lower()
            if response != 'y':
                print("❌ 跳过重复数据")
                return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO stock_analysis_results (
                    analysis_datetime, stock_name, stock_code,
                    value_score, growth_score, technical_score, total_score,
                    confidence_level, weight_config, market_environment,
                    detailed_analysis, investment_advice, risk_warnings
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['analysis_datetime'],
                data['stock_name'],
                data['stock_code'].upper(),
                data['value_score'],
                data['growth_score'],
                data['technical_score'],
                data['total_score'],
                data['confidence_level'],
                data['weight_config'],
                data['market_environment'],
                data.get('detailed_analysis', ''),
                data.get('investment_advice', ''),
                data.get('risk_warnings', '')
            ))
            
            conn.commit()
            record_id = cursor.lastrowid
            
            print(f"✅ 数据添加成功: {data['stock_name']} ({data['stock_code']}) - ID: {record_id}")
            return True
            
        except Exception as e:
            print(f"❌ 数据添加失败: {e}")
            return False
        finally:
            if conn:
                conn.close()
    
    def interactive_input(self):
        """交互式输入单条数据"""
        print("\n🎯 交互式添加股票分析数据")
        print("=" * 40)
        
        data = {}
        
        # 基础信息
        data['stock_name'] = input("股票名称: ").strip()
        data['stock_code'] = input("股票代码 (如SZ002862): ").strip().upper()
        
        # 评分信息
        try:
            data['value_score'] = float(input("价值策略评分 (0-100): "))
            data['growth_score'] = float(input("成长策略评分 (0-100): "))
            data['technical_score'] = float(input("技术策略评分 (0-100): "))
            data['total_score'] = float(input("综合总分 (0-100): "))
        except ValueError:
            print("❌ 评分必须是数字")
            return False
        
        # 其他信息
        data['confidence_level'] = input("置信度等级 (高/中/低): ").strip()
        data['weight_config'] = input("权重配置 (用户指定/系统默认): ").strip()
        data['market_environment'] = input("市场环境 (牛市/熊市/震荡市/默认): ").strip()
        
        # 可选信息
        print("\n📝 可选信息 (直接回车跳过):")
        data['detailed_analysis'] = input("详细分析: ").strip()
        data['investment_advice'] = input("投资建议: ").strip()
        data['risk_warnings'] = input("风险提示: ").strip()
        
        return self.add_single_record(data)

    def import_from_csv(self, csv_file: str) -> Tuple[int, int]:
        """从CSV文件批量导入数据"""
        success_count = 0
        error_count = 0

        try:
            with open(csv_file, 'r', encoding='utf-8-sig') as file:
                reader = csv.DictReader(file)

                print(f"\n📊 开始从 {csv_file} 导入数据...")
                print("=" * 50)

                for row_num, row in enumerate(reader, 1):
                    print(f"\n处理第 {row_num} 行: {row.get('stock_name', 'Unknown')} ({row.get('stock_code', 'Unknown')})")

                    if self.add_single_record(row):
                        success_count += 1
                    else:
                        error_count += 1
                        print(f"❌ 第 {row_num} 行导入失败")

        except FileNotFoundError:
            print(f"❌ 文件未找到: {csv_file}")
            return 0, 1
        except Exception as e:
            print(f"❌ CSV导入失败: {e}")
            return 0, 1

        print(f"\n📈 导入完成:")
        print(f"✅ 成功: {success_count} 条")
        print(f"❌ 失败: {error_count} 条")

        return success_count, error_count

    def create_csv_template(self, template_file: str = "stock_data_template.csv"):
        """创建CSV模板文件"""
        headers = [
            'analysis_datetime', 'stock_name', 'stock_code',
            'value_score', 'growth_score', 'technical_score', 'total_score',
            'confidence_level', 'weight_config', 'market_environment',
            'detailed_analysis', 'investment_advice', 'risk_warnings'
        ]

        sample_data = [
            '2024-12-18 15:30:00', '实丰文化', 'SZ002862',
            '45.0', '62.0', '48.0', '52.3',
            '中', '系统默认', '震荡市',
            '价值策略45分：估值偏高但财务结构尚可', '谨慎考虑 - 业绩改善明显但估值仍然偏高', '估值风险：PE过高存在回调风险'
        ]

        try:
            with open(template_file, 'w', encoding='utf-8-sig', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(headers)
                writer.writerow(sample_data)

            print(f"✅ CSV模板已创建: {template_file}")
            print("📝 请编辑模板文件，然后使用 --csv 参数导入")

        except Exception as e:
            print(f"❌ 模板创建失败: {e}")

    def show_recent_records(self, limit: int = 5):
        """显示最近的记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT stock_name, stock_code, total_score, confidence_level, analysis_datetime
                FROM stock_analysis_results
                ORDER BY created_at DESC
                LIMIT ?
            ''', (limit,))

            records = cursor.fetchall()

            if records:
                print(f"\n📋 最近 {len(records)} 条记录:")
                print("-" * 80)
                print(f"{'股票名称':<10} {'代码':<10} {'总分':<8} {'置信度':<8} {'分析时间':<20}")
                print("-" * 80)

                for record in records:
                    name, code, score, confidence, datetime_str = record
                    print(f"{name:<10} {code:<10} {score:<8.1f} {confidence:<8} {datetime_str:<20}")
            else:
                print("📭 暂无数据记录")

        except Exception as e:
            print(f"❌ 查询失败: {e}")
        finally:
            if conn:
                conn.close()


def main():
    """主函数 - 命令行界面"""
    parser = argparse.ArgumentParser(
        description='智能股票分析系统v2.0 - 数据导入工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python stock_data_importer.py                    # 交互式添加数据
  python stock_data_importer.py --csv data.csv     # 从CSV文件导入
  python stock_data_importer.py --template         # 创建CSV模板
  python stock_data_importer.py --recent 10        # 查看最近10条记录
        '''
    )

    parser.add_argument('--csv', type=str, help='从CSV文件批量导入数据')
    parser.add_argument('--template', action='store_true', help='创建CSV模板文件')
    parser.add_argument('--recent', type=int, default=5, help='显示最近的记录数量')
    parser.add_argument('--db', type=str, default='stock_analysis.db', help='数据库文件路径')

    args = parser.parse_args()

    # 创建导入器实例
    importer = StockDataImporter(args.db)

    print("🚀 智能股票分析系统v2.0 - 数据导入工具")
    print("=" * 50)

    if args.template:
        # 创建CSV模板
        importer.create_csv_template()

    elif args.csv:
        # 批量导入CSV数据
        importer.import_from_csv(args.csv)

    elif len(sys.argv) > 1 and '--recent' in sys.argv:
        # 显示最近记录
        importer.show_recent_records(args.recent)

    else:
        # 交互式输入
        try:
            while True:
                print("\n📋 选择操作:")
                print("1. 添加单条数据")
                print("2. 查看最近记录")
                print("3. 创建CSV模板")
                print("4. 退出")

                choice = input("\n请选择 (1-4): ").strip()

                if choice == '1':
                    importer.interactive_input()
                elif choice == '2':
                    count = input("显示记录数量 (默认5): ").strip()
                    count = int(count) if count.isdigit() else 5
                    importer.show_recent_records(count)
                elif choice == '3':
                    importer.create_csv_template()
                elif choice == '4':
                    print("👋 再见!")
                    break
                else:
                    print("❌ 无效选择，请重试")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见!")
        except Exception as e:
            print(f"\n❌ 程序异常: {e}")


if __name__ == "__main__":
    import sys
    main()
