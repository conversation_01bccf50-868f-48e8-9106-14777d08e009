#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试《多策略股票分析流程文档.md》中LLM工具函数集成的完整性
验证文档中的代码示例是否能正常工作
"""

import os
import tempfile

def test_document_examples():
    """测试文档中的代码示例"""
    print("🧪 测试文档中的LLM工具函数集成")
    print("=" * 50)
    
    # 使用临时数据库避免影响现有数据
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 测试6.3.2节的快速保存函数示例
        print("📋 测试6.3.2节 - 快速保存函数")
        
        from llm_stock_tools import quick_save_analysis
        
        # 文档中的示例数据
        analysis_result = {
            "股票名称": "实丰文化",
            "股票代码": "SZ002862",
            "价值策略评分": 45.0,
            "成长策略评分": 62.0,
            "技术策略评分": 48.0,
            "综合评分": 52.3,
            "置信度": "中",
            "权重配置": "系统默认",
            "市场环境": "震荡市",
            "详细分析": "价值策略45分：估值偏高但财务结构尚可；成长策略62分：2024年业绩显著改善；技术策略48分：短期技术面一般",
            "投资建议": "谨慎考虑 - 业绩改善明显但估值仍然偏高，适合风险偏好较高的成长型投资者",
            "风险提示": "估值风险：PE=241.04存在较大回调风险；业绩风险：盈利改善的可持续性存疑"
        }
        
        # 保存到数据库（使用临时数据库）
        result = quick_save_analysis(analysis_result, db_path)
        
        # 处理结果
        if result['success']:
            print(f"✅ 分析结果已保存到数据库，记录ID: {result['record_id']}")
        else:
            print(f"❌ 保存失败: {result['error']}")
            return False
        
        # 测试6.3.3节的标准保存函数示例
        print("\n📋 测试6.3.3节 - 标准保存函数")
        
        from llm_stock_tools import save_stock_analysis
        
        result2 = save_stock_analysis(
            stock_name="测试股票",
            stock_code="SH600000",
            value_score=75.0,
            growth_score=80.0,
            technical_score=70.0,
            total_score=75.0,
            confidence_level="高",
            weight_config="用户指定",
            market_environment="牛市",
            detailed_analysis="详细分析内容...",
            investment_advice="投资建议内容...",
            risk_warnings="风险提示内容...",
            db_path=db_path
        )
        
        if result2['success']:
            print(f"✅ 标准保存函数测试成功，记录ID: {result2['record_id']}")
        else:
            print(f"❌ 标准保存函数测试失败: {result2['error']}")
            return False
        
        # 测试6.3.7节的查询功能示例
        print("\n📋 测试6.3.7节 - 数据查询功能")
        
        from llm_stock_tools import get_recent_analysis, get_stock_history
        
        # 查看最近的分析记录
        recent_records = get_recent_analysis(limit=5, db_path=db_path)
        if recent_records['success']:
            print("📋 最近分析记录:")
            for record in recent_records['data']:
                print(f"  {record['stock_name']} ({record['stock_code']}): {record['total_score']}分")
        else:
            print(f"❌ 查询最近记录失败: {recent_records['message']}")
            return False
        
        # 查看特定股票的历史分析
        history = get_stock_history("SZ002862", limit=3, db_path=db_path)
        if history['success']:
            print(f"📈 {history['stock_code']} 历史分析记录:")
            for record in history['data']:
                print(f"  {record['analysis_datetime']}: {record['total_score']}分 ({record['confidence_level']}置信度)")
        else:
            print(f"❌ 查询股票历史失败: {history['message']}")
            return False
        
        # 测试6.3.4节的完整工作流程集成
        print("\n📋 测试6.3.4节 - 完整工作流程集成")
        
        def complete_stock_analysis_workflow(stock_name, stock_code, market_env=None):
            """完整的股票分析工作流程（文档示例）"""
            
            # 步骤1-5：执行股票分析（按照文档流程）
            if market_env:
                weight_config = "用户指定"
                market_environment = market_env
            else:
                weight_config = "系统默认"
                market_environment = "默认"
            
            # 模拟分析结果
            analysis_result = {
                "股票名称": stock_name,
                "股票代码": stock_code,
                "价值策略评分": 70.0,
                "成长策略评分": 75.0,
                "技术策略评分": 65.0,
                "综合评分": 70.5,
                "置信度": "中",
                "权重配置": weight_config,
                "市场环境": market_environment,
                "详细分析": "模拟分析内容...",
                "投资建议": "模拟投资建议...",
                "风险提示": "模拟风险提示..."
            }
            
            # 步骤6：自动保存到数据库
            save_result = quick_save_analysis(analysis_result, db_path)
            
            if save_result['success']:
                print(f"💾 分析结果已保存到数据库 (ID: {save_result['record_id']})")
                return {
                    'analysis_complete': True,
                    'data_saved': True,
                    'record_id': save_result['record_id'],
                    'analysis_result': analysis_result
                }
            else:
                print(f"❌ 数据保存失败: {save_result['error']}")
                return {
                    'analysis_complete': True,
                    'data_saved': False,
                    'error': save_result['error'],
                    'analysis_result': analysis_result
                }
        
        # 测试完整工作流程
        workflow_result = complete_stock_analysis_workflow("工作流程测试", "SZ000001", "震荡市")
        
        if workflow_result['data_saved']:
            print("✅ 完整工作流程集成测试成功")
        else:
            print("❌ 完整工作流程集成测试失败")
            return False
        
        # 测试7.3节的LLM工具函数集成
        print("\n📋 测试7.3节 - LLM工具函数集成")
        
        # 模拟LLM分析结果
        llm_analysis_result = {
            "股票名称": "LLM测试",
            "股票代码": "SH600001",
            "价值策略评分": 80.0,
            "成长策略评分": 85.0,
            "技术策略评分": 75.0,
            "综合评分": 80.5,
            "置信度": "高",
            "权重配置": "系统默认",
            "市场环境": "牛市"
        }
        
        # 在分析完成后调用
        save_result = quick_save_analysis(llm_analysis_result, db_path)
        if save_result['success']:
            print(f"💾 保存状态: ✅ 已保存到数据库 (记录ID: {save_result['record_id']})")
            print("✅ LLM工具函数集成测试成功")
        else:
            print(f"💾 保存状态: ❌ 保存失败 ({save_result['error']})")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 llm_stock_tools.py 文件在当前目录")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.unlink(db_path)

def main():
    """主函数"""
    print("🚀 智能股票分析系统v2.0 - 文档集成测试")
    print("=" * 60)
    print("测试《多策略股票分析流程文档.md》中的LLM工具函数集成")
    print()
    
    if test_document_examples():
        print("\n🎉 所有文档示例测试通过！")
        print("📋 文档中的LLM工具函数集成工作正常")
        print("\n✅ 验证项目:")
        print("  - 6.3.2节 快速保存函数示例")
        print("  - 6.3.3节 标准保存函数示例")
        print("  - 6.3.4节 完整工作流程集成")
        print("  - 6.3.7节 数据查询功能")
        print("  - 7.3节 LLM工具函数集成")
        print("\n🔗 LLM可以按照文档流程完成从分析到数据存储的完整自动化工作流程！")
    else:
        print("\n❌ 部分测试失败")
        print("请检查 llm_stock_tools.py 文件是否存在且功能正常")

if __name__ == "__main__":
    main()
