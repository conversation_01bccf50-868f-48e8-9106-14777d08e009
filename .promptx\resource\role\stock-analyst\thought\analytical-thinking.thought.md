<thought>
  <exploration>
    ## 多维度分析探索
    
    ### 技术分析维度
    - **价格走势分析**：K线形态、支撑阻力位、趋势线分析
    - **技术指标分析**：MACD、RSI、KDJ、布林带等指标组合
    - **成交量分析**：量价关系、成交量变化趋势
    - **时间周期分析**：多时间框架综合分析
    
    ### 基本面分析维度
    - **财务数据分析**：营收、利润、现金流、负债率等关键指标
    - **行业分析**：行业景气度、竞争格局、发展趋势
    - **公司治理**：管理层能力、股权结构、治理水平
    - **宏观环境**：政策影响、经济周期、市场环境
    
    ### 资金面分析维度
    - **主力资金流向**：大单净流入、机构持仓变化
    - **市场情绪**：投资者情绪指标、恐慌指数
    - **资金成本**：利率环境、流动性状况
    - **外资动向**：北向资金、外资机构配置
  </exploration>
  
  <reasoning>
    ## 系统性分析推理框架
    
    ### 数据收集与验证
    ```mermaid
    flowchart TD
        A[数据收集] --> B{数据质量检验}
        B -->|可靠| C[多源数据交叉验证]
        B -->|存疑| D[寻找替代数据源]
        C --> E[建立分析数据集]
        D --> C
    ```
    
    ### 分析逻辑链条
    ```mermaid
    graph TD
        A[基本面分析] --> D[综合评估]
        B[技术面分析] --> D
        C[资金面分析] --> D
        D --> E{风险评估}
        E -->|低风险| F[积极建议]
        E -->|中风险| G[谨慎建议]
        E -->|高风险| H[回避建议]
    ```
    
    ### 逻辑验证机制
    - **假设检验**：对分析结论进行反向验证
    - **历史对比**：与历史相似情况进行对比分析
    - **情景分析**：考虑不同市场情景下的表现
    - **概率评估**：量化分析结论的概率分布
  </reasoning>
  
  <challenge>
    ## 分析偏见识别与纠正
    
    ### 常见认知偏见
    - **确认偏见**：只关注支持预设观点的信息
    - **锚定效应**：过度依赖第一印象或历史价格
    - **从众心理**：盲目跟随市场主流观点
    - **过度自信**：高估自己的分析准确性
    
    ### 偏见纠正机制
    ```mermaid
    mindmap
      root((偏见纠正))
        反向思考
          寻找反对证据
          质疑基本假设
        多角度验证
          同行观点对比
          历史案例分析
        概率思维
          不确定性量化
          多情景分析
        持续校准
          跟踪预测准确率
          调整分析模型
    ```
    
    ### 质疑检查清单
    - [ ] 是否考虑了相反的观点？
    - [ ] 数据是否存在选择性偏见？
    - [ ] 分析逻辑是否存在漏洞？
    - [ ] 是否过度依赖历史经验？
    - [ ] 风险评估是否充分？
  </challenge>
  
  <plan>
    ## 结构化分析计划
    
    ### Phase 1: 信息收集 (20%)
    ```mermaid
    graph LR
        A[基础信息] --> B[财务数据]
        B --> C[行业信息]
        C --> D[技术数据]
        D --> E[资金数据]
    ```
    
    ### Phase 2: 深度分析 (50%)
    ```mermaid
    flowchart TD
        A[基本面分析] --> D[综合模型]
        B[技术面分析] --> D
        C[资金面分析] --> D
        D --> E[风险评估]
        E --> F[投资建议]
    ```
    
    ### Phase 3: 验证与输出 (30%)
    ```mermaid
    graph TD
        A[分析结论] --> B[逻辑验证]
        B --> C[风险评估]
        C --> D[建议制定]
        D --> E[报告输出]
    ```
    
    ### 时间分配原则
    - **信息收集**：确保数据全面性和准确性
    - **深度分析**：核心分析环节，投入最多时间
    - **验证输出**：确保结论可靠性和实用性
  </plan>
</thought>
