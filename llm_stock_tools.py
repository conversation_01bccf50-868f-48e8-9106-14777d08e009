#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能股票分析系统v2.0 - LLM工具函数
为LLM提供直接调用的股票分析数据存储工具

主要功能：
- save_stock_analysis: 保存单条股票分析结果
- save_batch_stock_analysis: 批量保存股票分析结果
- get_recent_analysis: 获取最近的分析记录
- get_stock_history: 获取指定股票的历史分析
"""

import sqlite3
import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union

class StockAnalysisTools:
    """LLM股票分析工具类"""
    
    def __init__(self, db_path: str = "stock_analysis.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库（内部方法）"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    analysis_datetime TEXT NOT NULL,
                    stock_name TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    value_score REAL NOT NULL,
                    growth_score REAL NOT NULL,
                    technical_score REAL NOT NULL,
                    total_score REAL NOT NULL,
                    confidence_level TEXT NOT NULL,
                    weight_config TEXT NOT NULL,
                    market_environment TEXT NOT NULL,
                    detailed_analysis TEXT,
                    investment_advice TEXT,
                    risk_warnings TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_code ON stock_analysis_results(stock_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_datetime ON stock_analysis_results(analysis_datetime)')
            
            conn.commit()
            
        except Exception as e:
            raise Exception(f"数据库初始化失败: {e}")
        finally:
            if conn:
                conn.close()
    
    def _validate_data(self, data: Dict) -> Tuple[bool, str]:
        """验证数据格式（内部方法）"""
        # 检查必需字段
        required_fields = [
            'stock_name', 'stock_code', 'value_score', 'growth_score',
            'technical_score', 'total_score', 'confidence_level',
            'weight_config', 'market_environment'
        ]
        
        for field in required_fields:
            if field not in data or data[field] is None or data[field] == '':
                return False, f"缺少必需字段: {field}"
        
        # 验证股票代码格式
        stock_code_pattern = r'^(SZ|SH)\d{6}$'
        if not re.match(stock_code_pattern, data['stock_code'].upper()):
            return False, f"股票代码格式错误: {data['stock_code']} (应为SZ000001或SH600000格式)"
        
        # 验证评分范围
        score_fields = ['value_score', 'growth_score', 'technical_score', 'total_score']
        for field in score_fields:
            try:
                score = float(data[field])
                if not 0 <= score <= 100:
                    return False, f"{field}必须在0-100之间，当前值: {score}"
            except (ValueError, TypeError):
                return False, f"{field}必须是数字，当前值: {data[field]}"
        
        # 验证置信度等级
        valid_confidence = ['高', '中', '低']
        if data['confidence_level'] not in valid_confidence:
            return False, f"置信度等级必须是: {', '.join(valid_confidence)}"
        
        # 验证市场环境
        valid_environments = ['牛市', '熊市', '震荡市', '默认']
        if data['market_environment'] not in valid_environments:
            return False, f"市场环境必须是: {', '.join(valid_environments)}"
        
        return True, "验证通过"


def save_stock_analysis(
    stock_name: str,
    stock_code: str,
    value_score: float,
    growth_score: float,
    technical_score: float,
    total_score: float,
    confidence_level: str,
    weight_config: str,
    market_environment: str,
    detailed_analysis: str = "",
    investment_advice: str = "",
    risk_warnings: str = "",
    analysis_datetime: str = None,
    db_path: str = "stock_analysis.db"
) -> Dict[str, Union[bool, int, str]]:
    """
    保存单条股票分析结果到数据库
    
    Args:
        stock_name (str): 股票名称，如"实丰文化"
        stock_code (str): 股票代码，如"SZ002862"或"SH600519"
        value_score (float): 价值策略评分，0-100
        growth_score (float): 成长策略评分，0-100
        technical_score (float): 技术策略评分，0-100
        total_score (float): 综合总分，0-100
        confidence_level (str): 置信度等级，"高"/"中"/"低"
        weight_config (str): 权重配置，"用户指定"/"系统默认"
        market_environment (str): 市场环境，"牛市"/"熊市"/"震荡市"/"默认"
        detailed_analysis (str, optional): 详细分析内容
        investment_advice (str, optional): 投资建议
        risk_warnings (str, optional): 风险提示
        analysis_datetime (str, optional): 分析时间，格式"YYYY-MM-DD HH:MM:SS"
        db_path (str, optional): 数据库文件路径
    
    Returns:
        Dict: 包含以下键值的字典
            - success (bool): 是否成功
            - record_id (int): 记录ID（成功时）
            - message (str): 结果信息
            - error (str): 错误信息（失败时）
    
    Example:
        >>> result = save_stock_analysis(
        ...     stock_name="实丰文化",
        ...     stock_code="SZ002862",
        ...     value_score=45.0,
        ...     growth_score=62.0,
        ...     technical_score=48.0,
        ...     total_score=52.3,
        ...     confidence_level="中",
        ...     weight_config="系统默认",
        ...     market_environment="震荡市",
        ...     detailed_analysis="价值策略45分：估值偏高但财务结构尚可",
        ...     investment_advice="谨慎考虑 - 业绩改善明显但估值仍然偏高",
        ...     risk_warnings="估值风险：PE过高存在回调风险"
        ... )
        >>> print(result)
        {'success': True, 'record_id': 1, 'message': '数据保存成功'}
    """
    
    # 构造数据字典
    data = {
        'stock_name': stock_name,
        'stock_code': stock_code.upper(),
        'value_score': value_score,
        'growth_score': growth_score,
        'technical_score': technical_score,
        'total_score': total_score,
        'confidence_level': confidence_level,
        'weight_config': weight_config,
        'market_environment': market_environment,
        'detailed_analysis': detailed_analysis,
        'investment_advice': investment_advice,
        'risk_warnings': risk_warnings,
        'analysis_datetime': analysis_datetime or datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    try:
        # 创建工具实例并验证数据
        tools = StockAnalysisTools(db_path)
        is_valid, message = tools._validate_data(data)
        
        if not is_valid:
            return {
                'success': False,
                'error': f"数据验证失败: {message}",
                'message': f"保存失败: {message}"
            }
        
        # 保存到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO stock_analysis_results (
                analysis_datetime, stock_name, stock_code,
                value_score, growth_score, technical_score, total_score,
                confidence_level, weight_config, market_environment,
                detailed_analysis, investment_advice, risk_warnings
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['analysis_datetime'], data['stock_name'], data['stock_code'],
            data['value_score'], data['growth_score'], data['technical_score'], data['total_score'],
            data['confidence_level'], data['weight_config'], data['market_environment'],
            data['detailed_analysis'], data['investment_advice'], data['risk_warnings']
        ))
        
        conn.commit()
        record_id = cursor.lastrowid
        
        return {
            'success': True,
            'record_id': record_id,
            'message': f"数据保存成功: {stock_name} ({stock_code}) - ID: {record_id}"
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': f"保存失败: {str(e)}"
        }
    finally:
        if 'conn' in locals():
            conn.close()


def save_batch_stock_analysis(
    analysis_list: List[Dict],
    db_path: str = "stock_analysis.db"
) -> Dict[str, Union[bool, int, List, str]]:
    """
    批量保存股票分析结果到数据库

    Args:
        analysis_list (List[Dict]): 股票分析数据列表，每个字典包含单条分析的所有字段
        db_path (str, optional): 数据库文件路径

    Returns:
        Dict: 包含以下键值的字典
            - success (bool): 是否全部成功
            - total_count (int): 总数量
            - success_count (int): 成功数量
            - failed_count (int): 失败数量
            - success_ids (List[int]): 成功保存的记录ID列表
            - failed_items (List[Dict]): 失败的项目及错误信息
            - message (str): 结果摘要

    Example:
        >>> analysis_data = [
        ...     {
        ...         'stock_name': '实丰文化',
        ...         'stock_code': 'SZ002862',
        ...         'value_score': 45.0,
        ...         'growth_score': 62.0,
        ...         'technical_score': 48.0,
        ...         'total_score': 52.3,
        ...         'confidence_level': '中',
        ...         'weight_config': '系统默认',
        ...         'market_environment': '震荡市'
        ...     },
        ...     # 更多数据...
        ... ]
        >>> result = save_batch_stock_analysis(analysis_data)
        >>> print(result)
        {'success': True, 'total_count': 2, 'success_count': 2, 'failed_count': 0, ...}
    """

    if not analysis_list:
        return {
            'success': False,
            'total_count': 0,
            'success_count': 0,
            'failed_count': 0,
            'success_ids': [],
            'failed_items': [],
            'message': "分析数据列表为空"
        }

    total_count = len(analysis_list)
    success_count = 0
    failed_count = 0
    success_ids = []
    failed_items = []

    for i, analysis_data in enumerate(analysis_list):
        try:
            # 确保必需字段存在
            required_fields = [
                'stock_name', 'stock_code', 'value_score', 'growth_score',
                'technical_score', 'total_score', 'confidence_level',
                'weight_config', 'market_environment'
            ]

            # 检查必需字段
            missing_fields = [field for field in required_fields if field not in analysis_data]
            if missing_fields:
                failed_count += 1
                failed_items.append({
                    'index': i,
                    'data': analysis_data,
                    'error': f"缺少必需字段: {', '.join(missing_fields)}"
                })
                continue

            # 调用单条保存函数
            result = save_stock_analysis(
                stock_name=analysis_data['stock_name'],
                stock_code=analysis_data['stock_code'],
                value_score=analysis_data['value_score'],
                growth_score=analysis_data['growth_score'],
                technical_score=analysis_data['technical_score'],
                total_score=analysis_data['total_score'],
                confidence_level=analysis_data['confidence_level'],
                weight_config=analysis_data['weight_config'],
                market_environment=analysis_data['market_environment'],
                detailed_analysis=analysis_data.get('detailed_analysis', ''),
                investment_advice=analysis_data.get('investment_advice', ''),
                risk_warnings=analysis_data.get('risk_warnings', ''),
                analysis_datetime=analysis_data.get('analysis_datetime'),
                db_path=db_path
            )

            if result['success']:
                success_count += 1
                success_ids.append(result['record_id'])
            else:
                failed_count += 1
                failed_items.append({
                    'index': i,
                    'data': analysis_data,
                    'error': result.get('error', '未知错误')
                })

        except Exception as e:
            failed_count += 1
            failed_items.append({
                'index': i,
                'data': analysis_data,
                'error': str(e)
            })

    return {
        'success': failed_count == 0,
        'total_count': total_count,
        'success_count': success_count,
        'failed_count': failed_count,
        'success_ids': success_ids,
        'failed_items': failed_items,
        'message': f"批量保存完成: 总计{total_count}条，成功{success_count}条，失败{failed_count}条"
    }


def get_recent_analysis(
    limit: int = 10,
    db_path: str = "stock_analysis.db"
) -> Dict[str, Union[bool, List, str]]:
    """
    获取最近的股票分析记录

    Args:
        limit (int, optional): 返回记录数量，默认10条
        db_path (str, optional): 数据库文件路径

    Returns:
        Dict: 包含以下键值的字典
            - success (bool): 是否成功
            - data (List[Dict]): 分析记录列表
            - count (int): 记录数量
            - message (str): 结果信息

    Example:
        >>> result = get_recent_analysis(limit=5)
        >>> if result['success']:
        ...     for record in result['data']:
        ...         print(f"{record['stock_name']} ({record['stock_code']}): {record['total_score']}分")
    """

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT
                id, analysis_datetime, stock_name, stock_code,
                value_score, growth_score, technical_score, total_score,
                confidence_level, weight_config, market_environment,
                detailed_analysis, investment_advice, risk_warnings,
                created_at
            FROM stock_analysis_results
            ORDER BY created_at DESC
            LIMIT ?
        ''', (limit,))

        records = cursor.fetchall()

        # 转换为字典列表
        columns = [
            'id', 'analysis_datetime', 'stock_name', 'stock_code',
            'value_score', 'growth_score', 'technical_score', 'total_score',
            'confidence_level', 'weight_config', 'market_environment',
            'detailed_analysis', 'investment_advice', 'risk_warnings',
            'created_at'
        ]

        data = []
        for record in records:
            data.append(dict(zip(columns, record)))

        return {
            'success': True,
            'data': data,
            'count': len(data),
            'message': f"成功获取{len(data)}条最近分析记录"
        }

    except Exception as e:
        return {
            'success': False,
            'data': [],
            'count': 0,
            'message': f"查询失败: {str(e)}"
        }
    finally:
        if 'conn' in locals():
            conn.close()


def get_stock_history(
    stock_code: str,
    limit: int = 10,
    db_path: str = "stock_analysis.db"
) -> Dict[str, Union[bool, List, str]]:
    """
    获取指定股票的历史分析记录

    Args:
        stock_code (str): 股票代码，如"SZ002862"
        limit (int, optional): 返回记录数量，默认10条
        db_path (str, optional): 数据库文件路径

    Returns:
        Dict: 包含以下键值的字典
            - success (bool): 是否成功
            - data (List[Dict]): 分析记录列表
            - count (int): 记录数量
            - stock_code (str): 查询的股票代码
            - message (str): 结果信息

    Example:
        >>> result = get_stock_history("SZ002862", limit=5)
        >>> if result['success']:
        ...     print(f"股票 {result['stock_code']} 的历史分析:")
        ...     for record in result['data']:
        ...         print(f"  {record['analysis_datetime']}: {record['total_score']}分")
    """

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT
                id, analysis_datetime, stock_name, stock_code,
                value_score, growth_score, technical_score, total_score,
                confidence_level, weight_config, market_environment,
                detailed_analysis, investment_advice, risk_warnings,
                created_at
            FROM stock_analysis_results
            WHERE stock_code = ?
            ORDER BY analysis_datetime DESC
            LIMIT ?
        ''', (stock_code.upper(), limit))

        records = cursor.fetchall()

        # 转换为字典列表
        columns = [
            'id', 'analysis_datetime', 'stock_name', 'stock_code',
            'value_score', 'growth_score', 'technical_score', 'total_score',
            'confidence_level', 'weight_config', 'market_environment',
            'detailed_analysis', 'investment_advice', 'risk_warnings',
            'created_at'
        ]

        data = []
        for record in records:
            data.append(dict(zip(columns, record)))

        return {
            'success': True,
            'data': data,
            'count': len(data),
            'stock_code': stock_code.upper(),
            'message': f"成功获取股票 {stock_code.upper()} 的{len(data)}条历史分析记录"
        }

    except Exception as e:
        return {
            'success': False,
            'data': [],
            'count': 0,
            'stock_code': stock_code.upper(),
            'message': f"查询失败: {str(e)}"
        }
    finally:
        if 'conn' in locals():
            conn.close()


# 便捷函数：为LLM提供更简单的调用接口
def quick_save_analysis(analysis_result: Dict, db_path: str = "stock_analysis.db") -> Dict:
    """
    快速保存分析结果的便捷函数

    Args:
        analysis_result (Dict): 包含分析结果的字典，支持灵活的字段名
        db_path (str, optional): 数据库文件路径

    Returns:
        Dict: 保存结果

    Example:
        >>> # LLM分析完成后的结果
        >>> analysis = {
        ...     "股票名称": "实丰文化",
        ...     "股票代码": "SZ002862",
        ...     "价值策略评分": 45.0,
        ...     "成长策略评分": 62.0,
        ...     "技术策略评分": 48.0,
        ...     "综合评分": 52.3,
        ...     "置信度": "中",
        ...     "权重配置": "系统默认",
        ...     "市场环境": "震荡市",
        ...     "详细分析": "...",
        ...     "投资建议": "...",
        ...     "风险提示": "..."
        ... }
        >>> result = quick_save_analysis(analysis)
    """

    # 字段映射：支持中文字段名到英文字段名的转换
    field_mapping = {
        '股票名称': 'stock_name',
        '股票代码': 'stock_code',
        '价值策略评分': 'value_score',
        '成长策略评分': 'growth_score',
        '技术策略评分': 'technical_score',
        '综合评分': 'total_score',
        '综合得分': 'total_score',
        '置信度': 'confidence_level',
        '置信度等级': 'confidence_level',
        '权重配置': 'weight_config',
        '市场环境': 'market_environment',
        '详细分析': 'detailed_analysis',
        '投资建议': 'investment_advice',
        '风险提示': 'risk_warnings',
        '分析时间': 'analysis_datetime'
    }

    # 转换字段名
    converted_data = {}
    for key, value in analysis_result.items():
        # 如果是中文字段名，转换为英文
        if key in field_mapping:
            converted_data[field_mapping[key]] = value
        # 如果已经是英文字段名，直接使用
        elif key in ['stock_name', 'stock_code', 'value_score', 'growth_score',
                     'technical_score', 'total_score', 'confidence_level',
                     'weight_config', 'market_environment', 'detailed_analysis',
                     'investment_advice', 'risk_warnings', 'analysis_datetime']:
            converted_data[key] = value

    # 检查必需字段
    required_fields = [
        'stock_name', 'stock_code', 'value_score', 'growth_score',
        'technical_score', 'total_score', 'confidence_level',
        'weight_config', 'market_environment'
    ]

    missing_fields = [field for field in required_fields if field not in converted_data]
    if missing_fields:
        return {
            'success': False,
            'error': f"缺少必需字段: {', '.join(missing_fields)}",
            'message': f"保存失败，缺少字段: {', '.join(missing_fields)}"
        }

    # 调用标准保存函数
    return save_stock_analysis(
        stock_name=converted_data['stock_name'],
        stock_code=converted_data['stock_code'],
        value_score=converted_data['value_score'],
        growth_score=converted_data['growth_score'],
        technical_score=converted_data['technical_score'],
        total_score=converted_data['total_score'],
        confidence_level=converted_data['confidence_level'],
        weight_config=converted_data['weight_config'],
        market_environment=converted_data['market_environment'],
        detailed_analysis=converted_data.get('detailed_analysis', ''),
        investment_advice=converted_data.get('investment_advice', ''),
        risk_warnings=converted_data.get('risk_warnings', ''),
        analysis_datetime=converted_data.get('analysis_datetime'),
        db_path=db_path
    )


# 主要导出的函数列表
__all__ = [
    'save_stock_analysis',
    'save_batch_stock_analysis',
    'get_recent_analysis',
    'get_stock_history',
    'quick_save_analysis'
]
