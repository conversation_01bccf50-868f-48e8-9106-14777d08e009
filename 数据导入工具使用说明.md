# 智能股票分析系统v2.0 - 数据导入工具使用说明

## 工具概述

`stock_data_importer.py` 是为智能股票分析系统v2.0设计的数据导入工具，提供简单易用的接口来向SQLite数据库添加股票分析结果。

### 主要功能
- ✅ **交互式添加**：通过命令行界面逐步输入数据
- ✅ **批量导入**：从CSV文件批量导入多只股票数据
- ✅ **数据验证**：自动验证数据格式和完整性
- ✅ **重复检测**：自动检测并处理重复数据
- ✅ **模板生成**：自动生成CSV导入模板

---

## 快速开始

### 1. 环境要求
- Python 3.6+
- 无需额外依赖包（使用Python标准库）

### 2. 基本使用
```bash
# 交互式添加数据
python stock_data_importer.py

# 从CSV文件批量导入
python stock_data_importer.py --csv data.csv

# 创建CSV模板
python stock_data_importer.py --template

# 查看最近记录
python stock_data_importer.py --recent 10
```

---

## 详细使用指南

### 交互式添加数据

运行脚本后选择"添加单条数据"，按提示输入以下信息：

#### 必需字段
- **股票名称**：如"实丰文化"
- **股票代码**：标准格式，如"SZ002862"或"SH600519"
- **价值策略评分**：0-100分
- **成长策略评分**：0-100分
- **技术策略评分**：0-100分
- **综合总分**：0-100分
- **置信度等级**：高/中/低
- **权重配置**：用户指定/系统默认
- **市场环境**：牛市/熊市/震荡市/默认

#### 可选字段
- **详细分析**：具体的分析内容
- **投资建议**：投资建议说明
- **风险提示**：风险警告信息

### 批量导入CSV数据

#### 1. 创建CSV模板
```bash
python stock_data_importer.py --template
```
这将创建 `stock_data_template.csv` 文件，包含所有必需的列标题和示例数据。

#### 2. 编辑CSV文件
打开模板文件，按照以下格式填入数据：

| 字段名 | 类型 | 示例 | 说明 |
|--------|------|------|------|
| analysis_datetime | 文本 | 2024-12-18 15:30:00 | 分析时间 |
| stock_name | 文本 | 实丰文化 | 股票名称 |
| stock_code | 文本 | SZ002862 | 股票代码 |
| value_score | 数字 | 45.0 | 价值策略评分 |
| growth_score | 数字 | 62.0 | 成长策略评分 |
| technical_score | 数字 | 48.0 | 技术策略评分 |
| total_score | 数字 | 52.3 | 综合总分 |
| confidence_level | 文本 | 中 | 置信度等级 |
| weight_config | 文本 | 系统默认 | 权重配置 |
| market_environment | 文本 | 震荡市 | 市场环境 |
| detailed_analysis | 文本 | 价值策略45分... | 详细分析（可选） |
| investment_advice | 文本 | 谨慎考虑... | 投资建议（可选） |
| risk_warnings | 文本 | 估值风险... | 风险提示（可选） |

#### 3. 导入数据
```bash
python stock_data_importer.py --csv your_data.csv
```

---

## 数据验证规则

### 股票代码格式
- 必须以SZ或SH开头
- 后跟6位数字
- 示例：SZ002862、SH600519

### 评分范围
- 所有评分字段必须在0-100之间
- 支持小数，如52.3

### 置信度等级
- 只能是：高、中、低

### 市场环境
- 只能是：牛市、熊市、震荡市、默认

---

## 错误处理

### 常见错误及解决方案

#### 1. 股票代码格式错误
```
❌ 股票代码格式错误: 002862 (应为SZ000001或SH600000格式)
```
**解决方案**：确保股票代码以SZ或SH开头

#### 2. 评分超出范围
```
❌ value_score必须在0-100之间
```
**解决方案**：检查评分是否在有效范围内

#### 3. 重复数据
```
⚠️ 发现重复数据: SZ002862 2024-12-18 15:30:00
是否覆盖? (y/N):
```
**解决方案**：选择y覆盖或N跳过

#### 4. CSV文件格式错误
```
❌ CSV导入失败: 'utf-8' codec can't decode byte
```
**解决方案**：确保CSV文件使用UTF-8编码保存

---

## 命令行参数

```bash
python stock_data_importer.py [选项]

选项:
  --csv FILE        从CSV文件批量导入数据
  --template        创建CSV模板文件
  --recent N        显示最近N条记录 (默认5)
  --db PATH         指定数据库文件路径 (默认stock_analysis.db)
  --help           显示帮助信息
```

### 使用示例

```bash
# 使用自定义数据库文件
python stock_data_importer.py --db my_stocks.db

# 查看最近20条记录
python stock_data_importer.py --recent 20

# 从特定CSV文件导入
python stock_data_importer.py --csv stocks_2024.csv
```

---

## 数据库结构

工具会自动创建以下表结构：

```sql
CREATE TABLE stock_analysis_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    analysis_datetime TEXT NOT NULL,
    stock_name TEXT NOT NULL,
    stock_code TEXT NOT NULL,
    value_score REAL NOT NULL,
    growth_score REAL NOT NULL,
    technical_score REAL NOT NULL,
    total_score REAL NOT NULL,
    confidence_level TEXT NOT NULL,
    weight_config TEXT NOT NULL,
    market_environment TEXT NOT NULL,
    detailed_analysis TEXT,
    investment_advice TEXT,
    risk_warnings TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 最佳实践

### 1. 数据准备
- 使用Excel或其他工具准备数据，然后保存为CSV格式
- 确保数据完整性，避免空值
- 统一股票代码格式（大写SZ/SH前缀）

### 2. 批量导入
- 先创建模板文件了解格式
- 小批量测试后再大批量导入
- 定期备份数据库文件

### 3. 数据维护
- 定期检查重复数据
- 使用 `--recent` 参数验证导入结果
- 保持数据的时效性

---

## 故障排除

### 1. 权限问题
如果遇到数据库文件权限错误：
```bash
chmod 666 stock_analysis.db
```

### 2. 编码问题
CSV文件保存时选择UTF-8编码，避免中文乱码

### 3. 路径问题
确保脚本和数据库文件在同一目录，或使用绝对路径

---

## 技术支持

如遇到问题，请检查：
1. Python版本是否为3.6+
2. 文件路径是否正确
3. 数据格式是否符合要求
4. 数据库文件是否有写入权限

更多技术细节请参考 `多策略股票分析流程文档.md`。
