#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多策略股票分析系统
基于10大投资策略的自动化股票评估工具
"""

import os
import json
import datetime
from typing import Dict, List, Tuple, Any
import re

class MultiStrategyStockAnalyzer:
    """多策略股票分析器"""
    
    def __init__(self):
        self.strategies_path = "."
        self.output_path = r"E:\conding\交易策略\test"
        self.strategies = self._load_strategies()
        self.stock_tools = StockDataTools()
        
    def _load_strategies(self) -> Dict[str, Dict]:
        """加载10大投资策略"""
        strategies = {}
        strategy_folders = [
            "01巴菲特主营集中选股战法",
            "02格雷厄姆高股息低估值战法", 
            "03戴维斯业绩连续增长战法",
            "04费雪创业板低估战法",
            "05德瑞曼净利润大增战法",
            "06戴维斯型风格策略",
            "07约翰·邓普顿风格策略",
            "08约翰·内夫10日线优质股战法",
            "09麦克尔·普里斯典型价值投资法",
            "10约翰·内夫风格短线策略"
        ]
        
        for folder in strategy_folders:
            strategy_file = self._find_strategy_file(folder)
            if strategy_file:
                strategies[folder] = self._parse_strategy_file(strategy_file)
                
        return strategies
    
    def _find_strategy_file(self, folder: str) -> str:
        """查找策略文件"""
        folder_path = os.path.join(self.strategies_path, folder)
        if os.path.exists(folder_path):
            for file in os.listdir(folder_path):
                if file.endswith('.md'):
                    return os.path.join(folder_path, file)
        return None
    
    def _parse_strategy_file(self, file_path: str) -> Dict:
        """解析策略文件，提取核心评估要素"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 应用奥卡姆剃刀原则，提取核心要素
            strategy_info = {
                'name': os.path.basename(file_path).replace('.md', ''),
                'weight': 10,  # 默认权重，可调整
                'criteria': self._extract_core_criteria(content),
                'content': content
            }
            return strategy_info
        except Exception as e:
            print(f"解析策略文件失败 {file_path}: {e}")
            return {}
    
    def _extract_core_criteria(self, content: str) -> List[Dict]:
        """提取核心评估标准（奥卡姆剃刀原则）"""
        # 简化的核心标准提取
        criteria = [
            {'name': '基本面评估', 'weight': 30, 'description': '财务健康度、盈利能力'},
            {'name': '估值水平', 'weight': 25, 'description': 'PE、PB、PEG等估值指标'},
            {'name': '成长性', 'weight': 20, 'description': '营收增长、利润增长'},
            {'name': '技术面', 'weight': 15, 'description': '技术指标、趋势分析'},
            {'name': '风险控制', 'weight': 10, 'description': '财务风险、市场风险'}
        ]
        return criteria

class StockDataTools:
    """股票数据获取工具"""
    
    def get_stock_full_data(self, stock_name: str, stock_code: str) -> Dict:
        """获取股票完整数据"""
        print(f"正在获取 {stock_name} ({stock_code}) 的详细数据...")
        
        # 这里需要集成实际的股票数据API
        # 示例数据结构
        mock_data = {
            'basic_info': {
                'name': stock_name,
                'code': stock_code,
                'industry': '文化传媒',
                'market_cap': 1000000000,
                'pe_ratio': 25.5,
                'pb_ratio': 2.1,
                'dividend_yield': 0.02
            },
            'financial_data': {
                'revenue_growth': 0.15,
                'profit_growth': 0.20,
                'roe': 0.12,
                'debt_ratio': 0.35,
                'current_ratio': 1.8
            },
            'technical_data': {
                'ma5': 12.5,
                'ma10': 12.8,
                'ma20': 13.2,
                'rsi': 65,
                'macd': 0.15
            },
            'trading_data': {
                'current_price': 12.35,
                'volume': 1500000,
                'turnover_rate': 0.05,
                'price_change': 0.02
            }
        }
        
        return mock_data

class StrategyScorer:
    """策略评分器"""
    
    def __init__(self):
        self.scoring_rules = self._init_scoring_rules()
    
    def _init_scoring_rules(self) -> Dict:
        """初始化评分规则"""
        return {
            '基本面评估': self._score_fundamentals,
            '估值水平': self._score_valuation,
            '成长性': self._score_growth,
            '技术面': self._score_technical,
            '风险控制': self._score_risk
        }
    
    def score_stock_by_strategy(self, stock_data: Dict, strategy: Dict) -> Dict:
        """按策略对股票评分"""
        scores = {}
        total_score = 0
        total_weight = 0
        
        for criterion in strategy['criteria']:
            criterion_name = criterion['name']
            weight = criterion['weight']
            
            if criterion_name in self.scoring_rules:
                score = self.scoring_rules[criterion_name](stock_data)
                weighted_score = score * weight / 100
                scores[criterion_name] = {
                    'score': score,
                    'weight': weight,
                    'weighted_score': weighted_score
                }
                total_score += weighted_score
                total_weight += weight
        
        return {
            'strategy_name': strategy['name'],
            'detailed_scores': scores,
            'total_score': round(total_score, 2),
            'total_weight': total_weight
        }
    
    def _score_fundamentals(self, stock_data: Dict) -> float:
        """基本面评分"""
        financial = stock_data.get('financial_data', {})
        roe = financial.get('roe', 0)
        debt_ratio = financial.get('debt_ratio', 1)
        current_ratio = financial.get('current_ratio', 0)
        
        # 简化评分逻辑
        score = 0
        if roe > 0.15: score += 30
        elif roe > 0.10: score += 20
        elif roe > 0.05: score += 10
        
        if debt_ratio < 0.3: score += 25
        elif debt_ratio < 0.5: score += 15
        elif debt_ratio < 0.7: score += 5
        
        if current_ratio > 2: score += 25
        elif current_ratio > 1.5: score += 15
        elif current_ratio > 1: score += 10
        
        return min(score, 100)
    
    def _score_valuation(self, stock_data: Dict) -> float:
        """估值评分"""
        basic = stock_data.get('basic_info', {})
        pe = basic.get('pe_ratio', 100)
        pb = basic.get('pb_ratio', 10)
        
        score = 0
        if pe < 15: score += 40
        elif pe < 25: score += 25
        elif pe < 35: score += 10
        
        if pb < 1.5: score += 40
        elif pb < 2.5: score += 25
        elif pb < 4: score += 10
        
        return min(score, 100)
    
    def _score_growth(self, stock_data: Dict) -> float:
        """成长性评分"""
        financial = stock_data.get('financial_data', {})
        revenue_growth = financial.get('revenue_growth', 0)
        profit_growth = financial.get('profit_growth', 0)
        
        score = 0
        if revenue_growth > 0.2: score += 30
        elif revenue_growth > 0.1: score += 20
        elif revenue_growth > 0.05: score += 10
        
        if profit_growth > 0.3: score += 40
        elif profit_growth > 0.15: score += 25
        elif profit_growth > 0.05: score += 15
        
        return min(score, 100)
    
    def _score_technical(self, stock_data: Dict) -> float:
        """技术面评分"""
        technical = stock_data.get('technical_data', {})
        trading = stock_data.get('trading_data', {})
        
        current_price = trading.get('current_price', 0)
        ma5 = technical.get('ma5', 0)
        ma10 = technical.get('ma10', 0)
        rsi = technical.get('rsi', 50)
        
        score = 50  # 基础分
        
        # 均线趋势
        if current_price > ma5 > ma10: score += 25
        elif current_price > ma5: score += 10
        
        # RSI指标
        if 30 < rsi < 70: score += 25
        elif 20 < rsi < 80: score += 10
        
        return min(score, 100)
    
    def _score_risk(self, stock_data: Dict) -> float:
        """风险控制评分"""
        financial = stock_data.get('financial_data', {})
        trading = stock_data.get('trading_data', {})
        
        debt_ratio = financial.get('debt_ratio', 1)
        turnover_rate = trading.get('turnover_rate', 0)
        
        score = 50  # 基础分
        
        # 财务风险
        if debt_ratio < 0.3: score += 30
        elif debt_ratio < 0.5: score += 15
        
        # 流动性风险
        if 0.02 < turnover_rate < 0.1: score += 20
        elif turnover_rate > 0.01: score += 10
        
        return min(score, 100)

def main():
    """主函数"""
    analyzer = MultiStrategyStockAnalyzer()
    scorer = StrategyScorer()
    
    # 示例：分析实丰文化
    stock_name = "实丰文化"
    stock_code = "SZ002862"
    
    print(f"开始分析 {stock_name} ({stock_code})")
    print("=" * 50)
    
    # 获取股票数据
    stock_data = analyzer.stock_tools.get_stock_full_data(stock_name, stock_code)
    
    # 多策略评分
    all_scores = []
    total_weighted_score = 0
    total_strategy_weight = 0
    
    for strategy_name, strategy in analyzer.strategies.items():
        if strategy:  # 确保策略加载成功
            score_result = scorer.score_stock_by_strategy(stock_data, strategy)
            all_scores.append(score_result)
            
            strategy_weight = strategy.get('weight', 10)
            weighted_contribution = score_result['total_score'] * strategy_weight / 100
            total_weighted_score += weighted_contribution
            total_strategy_weight += strategy_weight
            
            print(f"{strategy_name}: {score_result['total_score']:.2f}分")
    
    # 计算综合评分
    final_score = total_weighted_score / total_strategy_weight * 100 if total_strategy_weight > 0 else 0
    
    print("=" * 50)
    print(f"综合评分: {final_score:.2f}分")
    
    # 生成报告
    report_generator = ReportGenerator(analyzer.output_path)
    report_generator.generate_analysis_report(
        stock_data, all_scores, final_score, stock_name, stock_code
    )
    
    print(f"分析报告已保存到: {analyzer.output_path}")

class ReportGenerator:
    """报告生成器"""

    def __init__(self, output_path: str):
        self.output_path = output_path
        os.makedirs(output_path, exist_ok=True)

    def generate_analysis_report(self, stock_data: Dict, all_scores: List[Dict],
                               final_score: float, stock_name: str, stock_code: str):
        """生成分析报告"""
        date_str = datetime.datetime.now().strftime("%Y%m%d")
        filename = f"{stock_code}_{stock_name}_分析报告_{date_str}.md"
        filepath = os.path.join(self.output_path, filename)

        report_content = self._create_report_content(
            stock_data, all_scores, final_score, stock_name, stock_code
        )

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"报告已生成: {filepath}")

    def _create_report_content(self, stock_data: Dict, all_scores: List[Dict],
                             final_score: float, stock_name: str, stock_code: str) -> str:
        """创建报告内容"""
        date_str = datetime.datetime.now().strftime("%Y年%m月%d日")

        content = f"""# {stock_name} ({stock_code}) 多策略分析报告

## 报告信息
- **生成时间**: {date_str}
- **分析股票**: {stock_name} ({stock_code})
- **综合评分**: {final_score:.2f}分

## 股票基本信息
"""

        # 基本信息
        basic_info = stock_data.get('basic_info', {})
        content += f"""
- **股票名称**: {basic_info.get('name', 'N/A')}
- **股票代码**: {basic_info.get('code', 'N/A')}
- **所属行业**: {basic_info.get('industry', 'N/A')}
- **市值**: {basic_info.get('market_cap', 0):,.0f}元
- **PE比率**: {basic_info.get('pe_ratio', 'N/A')}
- **PB比率**: {basic_info.get('pb_ratio', 'N/A')}
- **股息率**: {basic_info.get('dividend_yield', 0):.2%}

## 财务数据
"""

        # 财务数据
        financial = stock_data.get('financial_data', {})
        content += f"""
- **营收增长率**: {financial.get('revenue_growth', 0):.2%}
- **利润增长率**: {financial.get('profit_growth', 0):.2%}
- **ROE**: {financial.get('roe', 0):.2%}
- **资产负债率**: {financial.get('debt_ratio', 0):.2%}
- **流动比率**: {financial.get('current_ratio', 0):.2f}

## 技术指标
"""

        # 技术数据
        technical = stock_data.get('technical_data', {})
        trading = stock_data.get('trading_data', {})
        content += f"""
- **当前价格**: {trading.get('current_price', 0):.2f}元
- **5日均线**: {technical.get('ma5', 0):.2f}元
- **10日均线**: {technical.get('ma10', 0):.2f}元
- **20日均线**: {technical.get('ma20', 0):.2f}元
- **RSI**: {technical.get('rsi', 0):.1f}
- **MACD**: {technical.get('macd', 0):.3f}
- **成交量**: {trading.get('volume', 0):,}股
- **换手率**: {trading.get('turnover_rate', 0):.2%}

## 各策略评分详情

"""

        # 各策略评分
        for score_result in all_scores:
            content += f"### {score_result['strategy_name']}\n"
            content += f"**总分**: {score_result['total_score']:.2f}分\n\n"

            for criterion_name, criterion_score in score_result['detailed_scores'].items():
                content += f"- **{criterion_name}**: {criterion_score['score']:.1f}分 "
                content += f"(权重: {criterion_score['weight']}%, "
                content += f"加权分: {criterion_score['weighted_score']:.2f})\n"
            content += "\n"

        # 综合评分和投资建议
        content += f"""## 综合评分结果

**最终得分**: {final_score:.2f}分

## 投资建议

"""

        # 根据评分给出建议
        if final_score >= 80:
            recommendation = "**强烈推荐** - 该股票在多个策略维度表现优秀，具有较高的投资价值。"
        elif final_score >= 70:
            recommendation = "**推荐** - 该股票整体表现良好，可以考虑投资，但需关注风险。"
        elif final_score >= 60:
            recommendation = "**谨慎考虑** - 该股票表现一般，投资需谨慎，建议进一步分析。"
        elif final_score >= 50:
            recommendation = "**不推荐** - 该股票表现较差，不建议投资。"
        else:
            recommendation = "**强烈不推荐** - 该股票在多个维度表现不佳，存在较高风险。"

        content += f"{recommendation}\n\n"

        content += """### 风险提示
1. 本分析基于历史数据和当前市场情况，不构成投资建议
2. 股市有风险，投资需谨慎
3. 建议结合其他分析方法和专业意见做出投资决策

---
*本报告由多策略股票分析系统自动生成*
"""

        return content

if __name__ == "__main__":
    main()
