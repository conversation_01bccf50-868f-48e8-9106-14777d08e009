#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM股票分析工具函数测试脚本
验证所有工具函数的正确性和可靠性
"""

import os
import tempfile
from llm_stock_tools import (
    save_stock_analysis,
    save_batch_stock_analysis,
    get_recent_analysis,
    get_stock_history,
    quick_save_analysis
)

def test_save_stock_analysis():
    """测试单条数据保存功能"""
    print("🧪 测试1: 单条数据保存功能")
    
    # 使用临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 测试有效数据
        result = save_stock_analysis(
            stock_name="测试股票",
            stock_code="SZ000001",
            value_score=75.0,
            growth_score=80.0,
            technical_score=70.0,
            total_score=75.5,
            confidence_level="高",
            weight_config="系统默认",
            market_environment="牛市",
            detailed_analysis="这是测试分析",
            investment_advice="这是测试建议",
            risk_warnings="这是测试风险提示",
            db_path=db_path
        )
        
        if result['success'] and 'record_id' in result:
            print("✅ 有效数据保存成功")
        else:
            print(f"❌ 有效数据保存失败: {result.get('error', '未知错误')}")
            return False
        
        # 测试无效股票代码
        invalid_result = save_stock_analysis(
            stock_name="测试股票",
            stock_code="000001",  # 缺少SZ/SH前缀
            value_score=75.0,
            growth_score=80.0,
            technical_score=70.0,
            total_score=75.5,
            confidence_level="高",
            weight_config="系统默认",
            market_environment="牛市",
            db_path=db_path
        )
        
        if not invalid_result['success'] and "股票代码格式错误" in invalid_result.get('error', ''):
            print("✅ 无效股票代码检测成功")
        else:
            print("❌ 无效股票代码检测失败")
            return False
        
        # 测试无效评分
        invalid_score_result = save_stock_analysis(
            stock_name="测试股票",
            stock_code="SZ000001",
            value_score=150.0,  # 超出范围
            growth_score=80.0,
            technical_score=70.0,
            total_score=75.5,
            confidence_level="高",
            weight_config="系统默认",
            market_environment="牛市",
            db_path=db_path
        )
        
        if not invalid_score_result['success']:
            print("✅ 无效评分检测成功")
        else:
            print("❌ 无效评分检测失败")
            return False
        
        return True
        
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.unlink(db_path)

def test_quick_save_analysis():
    """测试便捷保存功能"""
    print("\n🧪 测试2: 便捷保存功能（中文字段名）")
    
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 测试中文字段名
        analysis_data = {
            "股票名称": "测试股票",
            "股票代码": "SZ000002",
            "价值策略评分": 70.0,
            "成长策略评分": 75.0,
            "技术策略评分": 65.0,
            "综合评分": 70.5,
            "置信度": "中",
            "权重配置": "用户指定",
            "市场环境": "熊市",
            "详细分析": "测试中文字段分析",
            "投资建议": "测试中文字段建议",
            "风险提示": "测试中文字段风险"
        }
        
        result = quick_save_analysis(analysis_data, db_path)
        
        if result['success']:
            print("✅ 中文字段名保存成功")
        else:
            print(f"❌ 中文字段名保存失败: {result.get('error', '未知错误')}")
            return False
        
        # 测试英文字段名
        english_data = {
            "stock_name": "测试股票2",
            "stock_code": "SH600001",
            "value_score": 80.0,
            "growth_score": 85.0,
            "technical_score": 75.0,
            "total_score": 80.5,
            "confidence_level": "高",
            "weight_config": "系统默认",
            "market_environment": "震荡市"
        }
        
        result2 = quick_save_analysis(english_data, db_path)
        
        if result2['success']:
            print("✅ 英文字段名保存成功")
        else:
            print(f"❌ 英文字段名保存失败: {result2.get('error', '未知错误')}")
            return False
        
        return True
        
    finally:
        if os.path.exists(db_path):
            os.unlink(db_path)

def test_batch_save():
    """测试批量保存功能"""
    print("\n🧪 测试3: 批量保存功能")
    
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 准备批量数据（包含有效和无效数据）
        batch_data = [
            {
                'stock_name': '批量测试1',
                'stock_code': 'SZ000003',
                'value_score': 60.0,
                'growth_score': 70.0,
                'technical_score': 65.0,
                'total_score': 65.0,
                'confidence_level': '中',
                'weight_config': '系统默认',
                'market_environment': '牛市'
            },
            {
                'stock_name': '批量测试2',
                'stock_code': 'SH600002',
                'value_score': 75.0,
                'growth_score': 80.0,
                'technical_score': 70.0,
                'total_score': 75.0,
                'confidence_level': '高',
                'weight_config': '用户指定',
                'market_environment': '震荡市'
            },
            {
                'stock_name': '批量测试3',
                'stock_code': '000004',  # 无效代码
                'value_score': 85.0,
                'growth_score': 90.0,
                'technical_score': 80.0,
                'total_score': 85.0,
                'confidence_level': '高',
                'weight_config': '系统默认',
                'market_environment': '牛市'
            }
        ]
        
        result = save_batch_stock_analysis(batch_data, db_path)
        
        if result['success_count'] == 2 and result['failed_count'] == 1:
            print("✅ 批量保存功能正常（2成功1失败）")
        else:
            print(f"❌ 批量保存结果异常: 成功{result['success_count']}，失败{result['failed_count']}")
            return False
        
        return True
        
    finally:
        if os.path.exists(db_path):
            os.unlink(db_path)

def test_query_functions():
    """测试查询功能"""
    print("\n🧪 测试4: 查询功能")
    
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        # 先添加一些测试数据
        test_data = [
            {
                'stock_name': '查询测试1',
                'stock_code': 'SZ000005',
                'value_score': 60.0,
                'growth_score': 70.0,
                'technical_score': 65.0,
                'total_score': 65.0,
                'confidence_level': '中',
                'weight_config': '系统默认',
                'market_environment': '牛市'
            },
            {
                'stock_name': '查询测试2',
                'stock_code': 'SZ000005',  # 同一股票的另一条记录
                'value_score': 65.0,
                'growth_score': 75.0,
                'technical_score': 70.0,
                'total_score': 70.0,
                'confidence_level': '高',
                'weight_config': '用户指定',
                'market_environment': '震荡市'
            }
        ]
        
        # 保存测试数据
        save_batch_stock_analysis(test_data, db_path)
        
        # 测试获取最近记录
        recent_result = get_recent_analysis(limit=5, db_path=db_path)
        
        if recent_result['success'] and recent_result['count'] >= 2:
            print("✅ 获取最近记录功能正常")
        else:
            print(f"❌ 获取最近记录失败: {recent_result.get('message', '未知错误')}")
            return False
        
        # 测试获取股票历史
        history_result = get_stock_history('SZ000005', limit=5, db_path=db_path)
        
        if history_result['success'] and history_result['count'] == 2:
            print("✅ 获取股票历史功能正常")
        else:
            print(f"❌ 获取股票历史失败: {history_result.get('message', '未知错误')}")
            return False
        
        return True
        
    finally:
        if os.path.exists(db_path):
            os.unlink(db_path)

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行LLM工具函数测试")
    print("=" * 60)
    
    tests = [
        test_save_stock_analysis,
        test_quick_save_analysis,
        test_batch_save,
        test_query_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ 测试失败")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！LLM工具函数工作正常")
        print("\n📋 可用的工具函数:")
        print("  - save_stock_analysis: 保存单条分析结果")
        print("  - quick_save_analysis: 便捷保存（支持中文字段）")
        print("  - save_batch_stock_analysis: 批量保存")
        print("  - get_recent_analysis: 获取最近记录")
        print("  - get_stock_history: 获取股票历史")
        print("\n🔗 详细使用方法请参考: LLM工具函数说明.md")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    run_all_tests()
