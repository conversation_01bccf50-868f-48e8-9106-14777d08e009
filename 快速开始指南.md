# 智能股票分析系统v2.0 - 数据导入工具快速开始指南

## 📋 主要步骤目录

### 完整使用流程
1. **🔧 环境准备** - 安装Python环境，下载必需文件
2. **📊 获取数据** - 使用分析系统获取股票分析结果
3. **💾 导入数据** - 将分析结果导入SQLite数据库
4. **📈 查看成果** - 验证数据导入结果，查看历史记录
5. **🎯 持续使用** - 建立定期分析和数据管理流程

### 快速导航
- [环境准备](#第一步准备环境) → [运行测试](#第三步运行测试) → [开始使用](#第四步开始使用)
- [交互式添加](#方式1交互式添加推荐新手) → [批量导入](#方式2批量导入推荐批量操作) → [示例数据](#方式3使用示例数据)
- [常用命令](#-常用命令速查) → [数据格式](#-数据格式要求) → [常见问题](#️-常见问题)

---

## 🚀 5分钟快速上手

### 第一步：准备环境
确保你的系统已安装Python 3.6+：
```bash
python --version
```

### 第二步：下载文件
确保以下文件在同一目录：
- `stock_data_importer.py` - 主程序
- `示例数据.csv` - 示例数据文件
- `数据导入工具使用说明.md` - 详细说明

### 第三步：运行测试
验证工具是否正常工作：
```bash
python test_importer.py
```
看到"🎉 所有测试通过！"表示工具正常。

### 第四步：开始使用

#### 方式1：交互式添加（推荐新手）
```bash
python stock_data_importer.py
```
按提示输入股票信息即可。

#### 方式2：批量导入（推荐批量操作）
```bash
# 1. 创建模板
python stock_data_importer.py --template

# 2. 编辑 stock_data_template.csv 文件

# 3. 导入数据
python stock_data_importer.py --csv stock_data_template.csv
```

#### 方式3：使用示例数据
```bash
python stock_data_importer.py --csv 示例数据.csv
```

---

## 📋 常用命令速查

```bash
# 交互式添加数据
python stock_data_importer.py

# 批量导入CSV
python stock_data_importer.py --csv 文件名.csv

# 创建CSV模板
python stock_data_importer.py --template

# 查看最近10条记录
python stock_data_importer.py --recent 10

# 使用自定义数据库
python stock_data_importer.py --db my_database.db

# 查看帮助
python stock_data_importer.py --help
```

---

## 📊 数据格式要求

### 必需字段
| 字段 | 格式 | 示例 |
|------|------|------|
| 股票名称 | 文本 | 实丰文化 |
| 股票代码 | SZ/SH+6位数字 | SZ002862 |
| 价值策略评分 | 0-100数字 | 45.0 |
| 成长策略评分 | 0-100数字 | 62.0 |
| 技术策略评分 | 0-100数字 | 48.0 |
| 综合总分 | 0-100数字 | 52.3 |
| 置信度等级 | 高/中/低 | 中 |
| 权重配置 | 用户指定/系统默认 | 系统默认 |
| 市场环境 | 牛市/熊市/震荡市/默认 | 震荡市 |

### 可选字段
- 详细分析：具体分析内容
- 投资建议：投资建议说明
- 风险提示：风险警告信息

---

## ⚠️ 常见问题

### Q1：股票代码格式错误
**问题**：`❌ 股票代码格式错误: 002862`
**解决**：股票代码必须以SZ或SH开头，如SZ002862

### Q2：评分超出范围
**问题**：`❌ value_score必须在0-100之间`
**解决**：确保所有评分在0-100范围内

### Q3：CSV文件乱码
**问题**：中文显示乱码
**解决**：保存CSV文件时选择UTF-8编码

### Q4：重复数据处理
**问题**：`⚠️ 发现重复数据`
**解决**：选择y覆盖或N跳过重复数据

---

## 🎯 使用技巧

### 1. 批量导入最佳实践
- 先用小数据测试
- 确保数据格式正确
- 定期备份数据库

### 2. 数据验证
- 使用 `--recent` 查看导入结果
- 检查评分是否合理
- 验证股票代码格式

### 3. 错误处理
- 仔细阅读错误信息
- 逐行检查CSV数据
- 使用模板文件作为参考

---

## 📞 获取帮助

1. **查看详细说明**：阅读 `数据导入工具使用说明.md`
2. **运行测试**：执行 `python test_importer.py`
3. **查看命令帮助**：运行 `python stock_data_importer.py --help`

---

## 🎉 成功示例

### 交互式添加示例
```
🎯 交互式添加股票分析数据
========================================
股票名称: 实丰文化
股票代码 (如SZ002862): SZ002862
价值策略评分 (0-100): 45
成长策略评分 (0-100): 62
技术策略评分 (0-100): 48
综合总分 (0-100): 52.3
置信度等级 (高/中/低): 中
权重配置 (用户指定/系统默认): 系统默认
市场环境 (牛市/熊市/震荡市/默认): 震荡市

✅ 数据添加成功: 实丰文化 (SZ002862) - ID: 1
```

### 批量导入示例
```
📊 开始从 示例数据.csv 导入数据...
==================================================

处理第 1 行: 实丰文化 (SZ002862)
✅ 数据添加成功: 实丰文化 (SZ002862) - ID: 1

处理第 2 行: 贵州茅台 (SH600519)
✅ 数据添加成功: 贵州茅台 (SH600519) - ID: 2

📈 导入完成:
✅ 成功: 2 条
❌ 失败: 0 条
```

现在你可以开始使用数据导入工具了！🎉
