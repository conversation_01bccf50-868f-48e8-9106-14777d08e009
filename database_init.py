#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能股票分析系统v2.0 - 数据库初始化脚本
用于创建和管理股票分析结果的SQLite数据库
"""

import sqlite3
import os
import pandas as pd
from datetime import datetime
import json

class StockAnalysisDB:
    """股票分析数据库管理类"""
    
    def __init__(self, db_path="stock_analysis.db"):
        self.db_path = db_path
        
    def init_database(self):
        """初始化数据库和表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建分析结果表（优化版本 - 包含详细分析内容）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    analysis_datetime TEXT NOT NULL,
                    stock_name TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    value_score REAL NOT NULL,
                    growth_score REAL NOT NULL,
                    technical_score REAL NOT NULL,
                    total_score REAL NOT NULL,
                    confidence_level TEXT NOT NULL,
                    weight_config TEXT NOT NULL,
                    market_environment TEXT NOT NULL,
                    detailed_analysis TEXT,
                    investment_advice TEXT,
                    risk_warnings TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引提高查询性能
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_stock_code 
                ON stock_analysis_results(stock_code)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_analysis_datetime 
                ON stock_analysis_results(analysis_datetime)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_total_score 
                ON stock_analysis_results(total_score)
            ''')
            
            conn.commit()
            print(f"✅ 数据库初始化成功: {self.db_path}")
            print(f"📊 数据库位置: {os.path.abspath(self.db_path)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            return False
            
        finally:
            if conn:
                conn.close()
    
    def save_analysis_result(self, analysis_data):
        """保存分析结果到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO stock_analysis_results (
                    analysis_datetime, stock_name, stock_code,
                    value_score, growth_score, technical_score, total_score,
                    confidence_level, weight_config, market_environment,
                    detailed_analysis, investment_advice, risk_warnings
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                analysis_data['analysis_datetime'],
                analysis_data['stock_name'],
                analysis_data['stock_code'],
                analysis_data['value_score'],
                analysis_data['growth_score'],
                analysis_data['technical_score'],
                analysis_data['total_score'],
                analysis_data['confidence_level'],
                analysis_data['weight_config'],
                analysis_data['market_environment'],
                analysis_data.get('detailed_analysis', ''),
                analysis_data.get('investment_advice', ''),
                analysis_data.get('risk_warnings', '')
            ))
            
            conn.commit()
            record_id = cursor.lastrowid
            
            print(f"✅ 分析结果已保存 (ID: {record_id})")
            return record_id
            
        except Exception as e:
            print(f"❌ 数据保存失败: {e}")
            return None
            
        finally:
            if conn:
                conn.close()
    
    def query_stock_history(self, stock_code, limit=10):
        """查询指定股票的历史分析记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            df = pd.read_sql_query('''
                SELECT * FROM stock_analysis_results 
                WHERE stock_code = ? 
                ORDER BY analysis_datetime DESC 
                LIMIT ?
            ''', conn, params=(stock_code, limit))
            
            return df
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return pd.DataFrame()
            
        finally:
            if conn:
                conn.close()
    
    def query_top_stocks(self, limit=20):
        """查询评分最高的股票（最新记录）"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            df = pd.read_sql_query('''
                SELECT 
                    stock_code, stock_name, total_score, 
                    confidence_level, analysis_datetime,
                    CASE 
                        WHEN total_score >= 85 THEN '⭐⭐⭐⭐⭐ 优秀'
                        WHEN total_score >= 70 THEN '⭐⭐⭐⭐ 良好'
                        WHEN total_score >= 55 THEN '⭐⭐⭐ 一般'
                        WHEN total_score >= 40 THEN '⭐⭐ 较差'
                        ELSE '⭐ 很差'
                    END as rating
                FROM stock_analysis_results s1
                WHERE analysis_datetime = (
                    SELECT MAX(analysis_datetime) 
                    FROM stock_analysis_results s2 
                    WHERE s2.stock_code = s1.stock_code
                )
                ORDER BY total_score DESC 
                LIMIT ?
            ''', conn, params=(limit,))
            
            return df
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return pd.DataFrame()
            
        finally:
            if conn:
                conn.close()
    
    def export_to_csv(self, output_file="stock_analysis_export.csv"):
        """导出所有数据到CSV"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            df = pd.read_sql_query('''
                SELECT 
                    analysis_datetime,
                    stock_name,
                    stock_code,
                    value_score,
                    growth_score,
                    technical_score,
                    total_score,
                    confidence_level,
                    weight_config,
                    market_environment
                FROM stock_analysis_results 
                ORDER BY analysis_datetime DESC
            ''', conn)
            
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"✅ 数据已导出到: {output_file}")
            print(f"📊 导出记录数: {len(df)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return False
            
        finally:
            if conn:
                conn.close()
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 总记录数
            cursor.execute('SELECT COUNT(*) FROM stock_analysis_results')
            total_records = cursor.fetchone()[0]
            
            # 不同股票数量
            cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM stock_analysis_results')
            unique_stocks = cursor.fetchone()[0]
            
            # 最新分析时间
            cursor.execute('SELECT MAX(analysis_datetime) FROM stock_analysis_results')
            latest_analysis = cursor.fetchone()[0]
            
            # 平均评分
            cursor.execute('SELECT AVG(total_score) FROM stock_analysis_results')
            avg_score = cursor.fetchone()[0]
            
            stats = {
                'total_records': total_records,
                'unique_stocks': unique_stocks,
                'latest_analysis': latest_analysis,
                'average_score': round(avg_score, 2) if avg_score else 0,
                'database_size': f"{os.path.getsize(self.db_path) / 1024:.2f} KB"
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ 统计信息获取失败: {e}")
            return {}
            
        finally:
            if conn:
                conn.close()

def main():
    """主函数 - 演示数据库功能"""
    print("🚀 智能股票分析系统v2.0 - 数据库管理")
    print("=" * 50)
    
    # 初始化数据库
    db = StockAnalysisDB()
    
    if not db.init_database():
        return
    
    # 示例：保存分析结果（包含详细分析内容）
    sample_data = {
        'analysis_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'stock_name': '实丰文化',
        'stock_code': 'SZ002862',
        'value_score': 45.0,
        'growth_score': 62.0,
        'technical_score': 48.0,
        'total_score': 52.3,
        'confidence_level': '中',
        'weight_config': '系统默认',
        'market_environment': '震荡市',
        'detailed_analysis': '价值策略45分：估值偏高但财务结构尚可；成长策略62分：2024年业绩显著改善；技术策略48分：短期技术面一般',
        'investment_advice': '谨慎考虑 - 业绩改善明显但估值仍然偏高，适合风险偏好较高的成长型投资者',
        'risk_warnings': '估值风险：PE=241.04存在较大回调风险；业绩风险：盈利改善的可持续性存疑；市场风险：概念股波动性较大'
    }
    
    print("\n📝 保存示例数据...")
    record_id = db.save_analysis_result(sample_data)
    
    if record_id:
        print(f"✅ 示例数据保存成功 (ID: {record_id})")
    
    # 显示数据库统计
    print("\n📊 数据库统计信息:")
    stats = db.get_database_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n🎯 数据库初始化完成！")
    print("💡 使用说明:")
    print("  1. 数据库文件: stock_analysis.db")
    print("  2. 每次股票分析后会自动保存结果")
    print("  3. 可使用查询函数获取历史数据")
    print("  4. 支持导出CSV格式数据")

if __name__ == "__main__":
    main()
