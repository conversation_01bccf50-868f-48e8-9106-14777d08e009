# 智能股票分析系统 v3.0 - 批量分析版

## 快速开始

### 🔥 批量分析调用格式（推荐）
```
按照多策略股票分析流程文档.md的批量分析流程，分析股票列表 [CSV数据/文件路径] [可选：全局市场环境]
```

### 📊 批量分析使用示例
```
按照多策略股票分析流程文档.md的批量分析流程，分析股票列表 "贵州茅台,SH600519,牛市\n实丰文化,SZ002862,\n比亚迪,SZ002594,成长市"

按照多策略股票分析流程文档.md的批量分析流程，分析股票列表 stocks.csv 当前是牛市
```

### 📈 单股票分析调用格式（兼容保留）
```
按照多策略股票分析流程文档.md的单股票分析流程，分析股票 [股票名称] [股票代码] [可选：市场环境]
```

### 🎯 单股票使用示例
```
按照多策略股票分析流程文档.md的单股票分析流程，分析股票 实丰文化 SZ002862
按照多策略股票分析流程文档.md的单股票分析流程，分析股票 贵州茅台 SH600519 当前是牛市
```

---

## 一、核心原理

### 1.1 三大策略体系
- **价值策略**：寻找被低估的优质企业（估值+财务+股息）
- **成长策略**：投资业绩持续增长的企业（增长+创新+估值合理性）
- **技术策略**：基于技术分析进行择时投资（趋势+指标+成交量）

### 1.2 批量分析流程（主要模式）
1. **批量输入解析** - 解析CSV格式的股票列表
2. **权重配置策略** - 个股环境 > 全局环境 > 默认配置
3. **循环单股票分析** - 严格按顺序对每个股票执行完整的单股票分析流程
4. **即时数据保存** - 每个股票分析完成后立即保存到数据库
5. **进度跟踪显示** - 显示当前分析进度（第X/Y个股票）
6. **批量结果汇总** - 所有股票完成后显示汇总结果

### 1.3 单股票分析流程（兼容模式）
1. **权重配置** - 用户指定市场环境或使用默认配置
2. **数据获取** - 使用full命令获取股票数据
3. **三策略评分** - 各策略0-100分评分
4. **综合评分与JSON生成** - 加权计算总分并生成标准JSON格式
5. **结果展示** - 控制台显示完整分析
6. **数据保存** - 使用MCP工具直接保存JSON数据到数据库

---

## 二、批量输入格式

### 2.1 CSV输入格式标准
```csv
序号,股票名称,股票代码
1,贵州茅台,SH600519
2,实丰文化,SZ002862,
3,比亚迪,SZ002594
4,宁德时代,SZ300750
5,五粮液,SZ000858,
```

### 2.2 批量输入方式
- **直接CSV字符串**：在调用命令中直接提供CSV格式数据
- **CSV文件路径**：提供CSV文件的路径，系统自动读取
- **混合格式支持**：支持部分股票指定环境，部分使用全局环境

### 2.3 输入验证规则
- **股票代码格式**：必须使用标准格式（SZ/SH前缀）
- **市场环境可选**：可为空，使用全局环境或默认配置
- **容错处理**：跳过格式错误的行，继续处理其他股票

---

## 三、权重配置机制

### 3.1 权重配置表

| 市场环境 | 价值策略 | 成长策略 | 技术策略 | 使用场景 |
|----------|----------|----------|----------|----------|
| **牛市** | 30% | 50% | 20% | 用户指定"牛市"、"上涨"等 |
| **熊市** | 50% | 20% | 30% | 用户指定"熊市"、"下跌"等 |
| **成长市** | 25% | 60% | 15% | 用户指定"成长市"、"科技股"等 |
| **默认** | 40% | 35% | 25% | 用户未指定市场环境 |

### 3.2 批量配置优先级
1. **个股环境**：CSV中指定的个股市场环境（最高优先级）
2. **全局环境**：批量分析命令中指定的全局市场环境
3. **默认配置**：系统默认的均衡权重配置（最低优先级）

### 3.3 配置原则
- **个性化优先**：支持每个股票使用不同的权重配置
- **批量效率**：支持全局环境配置，提高批量分析效率
- **默认中性**：未指定时使用均衡配置，避免主观判断

---

## 四、数据获取与质量控制

### 4.1 批量数据获取流程
```
for each 股票 in 股票列表:
    full [股票名称] [股票代码]
    if 数据质量检查通过:
        继续分析
    else:
        记录错误，跳过该股票
```

### 4.2 单股票数据获取指令
```
full [股票名称] [股票代码]
```

### 4.3 必需数据清单
- **价值策略**：PE、PB、股息率、ROE、负债率
- **成长策略**：营收增长率、净利润增长率、PEG、研发投入
- **技术策略**：股价、均线、RSI、MACD、成交量

### 4.4 批量质量控制
- **完整度 ≥ 70%**：继续分析该股票
- **完整度 < 70%**：跳过该股票，记录到错误列表
- **异常检测**：ROE、PE、增长率等关键指标异常时给出警告
- **批量容错**：单个股票数据问题不影响整批分析的继续执行

---

## 五、三策略评分体系（保持不变）
按照多策略股票分析流程文档.md中定义的智能股票分析系统 v2.1完整流程，分析股票：世龙实业 SZ002748  当前可能是牛市

执行要求：
1. 严格按照文档中的分析流程执行
2. 使用文档中定义的三大策略评分体系
3. 应用用户主导的权重配置机制
4. 生成标准化分析报告格式，提供明确的评分结果、投资建议和风险提示
5. 执行数据持久化保存


### 5.1 价值策略（0-100分）按照多策略股票分析流程文档.md中定义的智能股票分析系统 v2.1完整流程，分析股票：世运电路 SH603920  当前可能是牛市

执行要求：
1. 严格按照文档中的分析流程执行
2. 使用文档中定义的三大策略评分体系
3. 应用用户主导的权重配置机制
4. 生成标准化分析报告格式，提供明确的评分结果、投资建议和风险提示
5. 执行数据持久化保存


- **估值水平**（40分）：PE < 15得40分，PE > 35得10分
- **财务质量**（35分）：ROE > 15%得20分，负债率 < 40%得15分
- **股息回报**（25分）：股息率 > 4%得25分

### 5.2 成长策略（0-100分）
- **增长质量**（50分）：营收增长 > 20%得25分，利润增长 > 25%得25分
- **创新能力**（30分）：研发占比 > 5%得30分
- **估值合理性**（20分）：PEG < 1得20分

### 5.3 技术策略（0-100分）
- **趋势强度**（40分）：多头排列得40分
- **技术指标**（35分）：RSI 50-70得20分，MACD金叉得15分
- **成交量能**（25分）：放量上涨得25分

---

## 六、综合评分与JSON生成

### 6.1 综合评分计算（保持不变）
```
综合得分 = 价值策略得分 × 权重 + 成长策略得分 × 权重 + 技术策略得分 × 权重
```

### 6.2 标准JSON格式生成（保持v2.1格式）
在完成综合评分后，LLM必须生成以下标准JSON格式：

```json
{
  "analysis_datetime": "YYYY-MM-DD HH:MM:SS",
  "stock_name": "股票名称",
  "stock_code": "股票代码",
  "value_score": 数值,
  "growth_score": 数值,
  "technical_score": 数值,
  "total_score": 数值,
  "confidence_level": "高/中/低",
  "weight_config": "个股指定/全局指定/系统默认",
  "market_environment": "牛市/熊市/成长市/震荡市/默认",
  "detailed_analysis": "详细分析内容",
  "investment_advice": "投资建议内容",
  "risk_warnings": "风险提示内容"
}
```

### 6.3 批量分析中的JSON处理
- **独立生成**：每个股票独立生成JSON，格式与单股票分析完全一致
- **即时保存**：每个股票的JSON生成后立即保存到数据库
- **无批量标识**：不添加任何批量相关的字段，保持数据库结构不变

### 6.4 评级标准（保持不变）

| 得分 | 评级 | 投资建议 |
|------|------|----------|
| 85-100分 | ⭐⭐⭐⭐⭐ | 强烈推荐 |
| 70-84分 | ⭐⭐⭐⭐ | 推荐 |
| 55-69分 | ⭐⭐⭐ | 谨慎考虑 |
| 40-54分 | ⭐⭐ | 不推荐 |
| <40分 | ⭐ | 强烈不推荐 |

---

## 七、数据库存储（保持v2.1结构）

### 7.1 数据库表结构（完全不变）
```sql
CREATE TABLE stock_analysis_results (
    id INTEGER PRIMARY KEY,
    analysis_datetime TEXT,
    stock_name TEXT,
    stock_code TEXT,
    value_score REAL,
    growth_score REAL,
    technical_score REAL,
    total_score REAL,
    confidence_level TEXT,
    weight_config TEXT,
    market_environment TEXT,
    detailed_analysis TEXT,
    investment_advice TEXT,
    risk_warnings TEXT
);
```

### 7.2 批量分析的数据保存策略
批量分析中，每个股票分析完成后立即保存：

```
for each 股票 in 股票列表:
    # 1. 执行完整的单股票分析流程
    执行单股票分析(股票)

    # 2. 立即保存该股票的分析结果
    create_record_MCP_SQLite_Server(
        table="stock_analysis_results",
        data={该股票的JSON数据}
    )

    # 3. 继续下一个股票
    继续下一个股票分析
```

### 7.3 数据保存原则
- **即时保存**：每个股票分析完成后立即保存，不等待整批完成
- **独立记录**：每个股票都是独立的数据库记录，无批量关联
- **格式一致**：保存的JSON格式与v2.1版本完全一致
- **错误隔离**：单个股票保存失败不影响其他股票的保存

---

## 八、LLM调用提示词

### 8.1 批量分析调用格式（主要模式）
```
按照多策略股票分析流程文档.md的批量分析流程，分析股票列表 [CSV数据/文件路径] [可选：全局市场环境]
```

### 8.2 批量分析执行流程
1. **解析批量输入**：解析CSV格式的股票列表
2. **循环单股票分析**：严格按顺序对每个股票执行以下完整流程：
   - 显示进度："正在分析第X/Y个股票：[股票名称]"
   - 确定该股票的权重配置（个股 > 全局 > 默认）
   - 使用full命令获取股票数据
   - 进行数据质量检查
   - 执行三策略评分
   - 计算综合评分并生成标准JSON格式
   - 立即保存该股票的分析结果到数据库
   - 显示该股票的分析结果
3. **批量结果汇总**：所有股票完成后显示汇总统计

### 8.3 单股票分析调用格式（兼容模式）
```
按照多策略股票分析流程文档.md的单股票分析流程，分析股票 [股票名称] [股票代码] [可选：市场环境]
```

### 8.4 单股票分析执行流程
1. 识别用户指定的市场环境或使用默认配置
2. 使用full命令获取股票数据
3. 进行数据质量检查
4. 执行三策略评分
5. 计算综合评分并生成标准JSON格式
6. 在控制台显示分析结果
7. 使用MCP工具保存JSON数据到数据库

---

## 九、控制台显示格式

### 9.1 批量分析显示模板
```
🚀 智能股票分析系统v3.0 - 批量分析进行中

📊 正在分析第1/3个股票：贵州茅台 (SH600519)
⚖️ 权重配置: 个股指定(牛市)
[执行完整的单股票分析流程...]
💾 ✅ 已保存到数据库

📊 正在分析第2/3个股票：比亚迪 (SZ002594)
⚖️ 权重配置: 个股指定(成长市)
[执行完整的单股票分析流程...]
💾 ✅ 已保存到数据库

📊 正在分析第3/3个股票：实丰文化 (SZ002862)
⚖️ 权重配置: 全局配置(牛市)
[执行完整的单股票分析流程...]
💾 ✅ 已保存到数据库

═══════════════════════════════════════════
🎉 批量分析完成汇总
⏰ 完成时间: [日期时间]
📈 分析统计: 总计3只，成功3只，失败0只
🌍 全局环境: 牛市
⚖️ 权重策略: 个股优先 > 全局配置 > 系统默认

📋 批量分析结果排行榜
┌─────────────────────────────────────────┐
│ 排名 │ 股票名称   │ 代码     │ 综合评分 │ 评级 │
├─────────────────────────────────────────┤
│  1   │ 贵州茅台   │ SH600519 │  92.5   │ ⭐⭐⭐⭐⭐ │
│  2   │ 比亚迪     │ SZ002594 │  78.3   │ ⭐⭐⭐⭐  │
│  3   │ 实丰文化   │ SZ002862 │  52.1   │ ⭐⭐⭐   │
└─────────────────────────────────────────┘

� 所有股票已独立保存到数据库
```

### 9.2 单股票分析显示模板（保持兼容）
```
🚀 智能股票分析系统v3.0 - 单股票分析结果

📊 [股票名称]([股票代码]) 分析概览
⏰ 分析时间: [日期时间]
⚖️ 权重配置: [用户指定/系统默认]
🎯 综合评分: [X.X]分 ⭐⭐⭐⭐⭐
🔍 置信度: [高/中/低]
💡 投资建议: [具体建议]

🎯 三策略评分雷达图
价值策略: [XX]分 ────── 成长策略: [XX]分
     │                      │
     │   综合评分: [XX]分     │
     │                      │
技术策略: [XX]分 ──────────────

📈 详细评分分析
💰 价值策略：[XX]/100分
🚀 成长策略：[XX]/100分
📊 技术策略：[XX]/100分

📄 JSON数据已生成
💾 MCP保存状态: ✅ 已保存到数据库
⏰ 保存时间: [YYYY-MM-DD HH:MM:SS]
```
---

## 十、批量分析错误处理

### 10.1 错误类型与处理策略
- **CSV格式错误**：跳过错误行，继续处理其他股票
- **股票代码无效**：记录错误，跳过该股票
- **数据获取失败**：记录错误，跳过该股票
- **数据质量不足**：记录警告，跳过该股票

### 10.2 错误恢复机制
- **即时处理**：每个股票分析失败时立即记录错误，继续下一个
- **独立保存**：成功分析的股票立即保存，不等待整批完成
- **错误隔离**：单个股票的错误不影响其他股票的分析和保存
- **进度继续**：显示错误信息后继续下一个股票的分析

### 10.3 批量分析质量保证
- **无最低成功率限制**：即使只有1个股票成功也会显示汇总结果
- **透明错误报告**：在汇总中明确显示成功和失败的数量
- **数据一致性**：每个股票的保存状态与显示状态完全一致

---

## 十一、使用注意事项

### 11.1 批量分析数据要求
- **CSV格式规范**：严格按照标准CSV格式提供股票列表
- **股票代码标准**：必须使用标准格式（SZ/SH前缀）
- **批量大小建议**：单次批量分析建议不超过20只股票
- **数据完整度**：每只股票数据完整度需要≥70%才能进行分析

### 11.2 性能与效率
- **分析时间**：批量分析时间约为单股票分析时间×股票数量
- **网络依赖**：需要稳定的网络连接获取股票数据
- **资源占用**：大批量分析可能占用较多系统资源

### 11.3 投资建议免责声明
- 本系统仅供参考，不构成投资建议
- 建议结合其他分析方法综合判断
- 投资有风险，决策需谨慎
- 批量分析结果应结合个股深度研究

---

## 十二、版本信息

**当前版本**: v3.0 - 批量分析版
**核心特性**:
- ✅ 批量股票分析支持（循环单股票分析）
- ✅ CSV格式输入解析
- ✅ 顺序处理机制（严格按列表顺序）
- ✅ 即时数据保存（每个股票完成后立即保存）
- ✅ 完全兼容单股票分析模式
- ✅ 保持v2.1数据库结构不变

**设计理念**: 基于Sean的矛盾分析方法论和奥卡姆剃刀原则
- 批量分析 = 多次独立的单股票分析循环
- 最小化对现有系统的改动
- 保持数据库结构和JSON格式完全不变
- 简洁的批量输入，完整的单股票分析输出

**重构优化**:
- 批量分析本质上是单股票分析的循环执行
- 移除所有批量会话管理功能
- 保持原有数据库表结构完全不变
- 每个股票独立分析、独立保存、独立显示

---

**文档结束**


