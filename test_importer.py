#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入工具测试脚本
用于验证 stock_data_importer.py 的功能
"""

import os
import sqlite3
from stock_data_importer import StockDataImporter

def test_database_creation():
    """测试数据库创建功能"""
    print("🧪 测试1: 数据库创建")
    
    # 使用测试数据库
    test_db = "test_stock_analysis.db"
    
    # 删除已存在的测试数据库
    if os.path.exists(test_db):
        os.remove(test_db)
    
    # 创建导入器实例
    importer = StockDataImporter(test_db)
    
    # 检查数据库文件是否创建
    if os.path.exists(test_db):
        print("✅ 数据库文件创建成功")
    else:
        print("❌ 数据库文件创建失败")
        return False
    
    # 检查表结构
    try:
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_analysis_results'")
        table_exists = cursor.fetchone() is not None
        
        if table_exists:
            print("✅ 数据表创建成功")
        else:
            print("❌ 数据表创建失败")
            return False
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False
    
    # 清理测试文件
    os.remove(test_db)
    return True

def test_data_validation():
    """测试数据验证功能"""
    print("\n🧪 测试2: 数据验证")
    
    importer = StockDataImporter("test_validation.db")
    
    # 测试有效数据
    valid_data = {
        'stock_name': '测试股票',
        'stock_code': 'SZ000001',
        'value_score': 75.0,
        'growth_score': 80.0,
        'technical_score': 70.0,
        'total_score': 75.5,
        'confidence_level': '高',
        'weight_config': '系统默认',
        'market_environment': '牛市'
    }
    
    is_valid, message = importer.validate_data(valid_data)
    if is_valid:
        print("✅ 有效数据验证通过")
    else:
        print(f"❌ 有效数据验证失败: {message}")
        return False
    
    # 测试无效股票代码
    invalid_data = valid_data.copy()
    invalid_data['stock_code'] = '000001'  # 缺少SZ/SH前缀
    
    is_valid, message = importer.validate_data(invalid_data)
    if not is_valid and "股票代码格式错误" in message:
        print("✅ 无效股票代码检测成功")
    else:
        print("❌ 无效股票代码检测失败")
        return False
    
    # 测试无效评分
    invalid_data = valid_data.copy()
    invalid_data['value_score'] = 150.0  # 超出范围
    
    is_valid, message = importer.validate_data(invalid_data)
    if not is_valid:
        print("✅ 无效评分检测成功")
    else:
        print("❌ 无效评分检测失败")
        return False
    
    # 清理测试文件
    if os.path.exists("test_validation.db"):
        os.remove("test_validation.db")
    
    return True

def test_data_insertion():
    """测试数据插入功能"""
    print("\n🧪 测试3: 数据插入")
    
    test_db = "test_insertion.db"
    importer = StockDataImporter(test_db)
    
    # 测试数据
    test_data = {
        'analysis_datetime': '2024-12-18 15:30:00',
        'stock_name': '测试股票',
        'stock_code': 'SZ000001',
        'value_score': 75.0,
        'growth_score': 80.0,
        'technical_score': 70.0,
        'total_score': 75.5,
        'confidence_level': '高',
        'weight_config': '系统默认',
        'market_environment': '牛市',
        'detailed_analysis': '这是测试分析',
        'investment_advice': '这是测试建议',
        'risk_warnings': '这是测试风险提示'
    }
    
    # 插入数据
    success = importer.add_single_record(test_data)
    if success:
        print("✅ 数据插入成功")
    else:
        print("❌ 数据插入失败")
        return False
    
    # 验证数据是否正确插入
    try:
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM stock_analysis_results WHERE stock_code = ?", ('SZ000001',))
        count = cursor.fetchone()[0]
        
        if count == 1:
            print("✅ 数据验证成功")
        else:
            print(f"❌ 数据验证失败，期望1条记录，实际{count}条")
            return False
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据验证异常: {e}")
        return False
    
    # 清理测试文件
    os.remove(test_db)
    return True

def test_csv_import():
    """测试CSV导入功能"""
    print("\n🧪 测试4: CSV导入")
    
    test_db = "test_csv.db"
    importer = StockDataImporter(test_db)
    
    # 创建测试CSV文件
    test_csv = "test_data.csv"
    csv_content = """analysis_datetime,stock_name,stock_code,value_score,growth_score,technical_score,total_score,confidence_level,weight_config,market_environment,detailed_analysis,investment_advice,risk_warnings
2024-12-18 15:30:00,测试股票1,SZ000001,75.0,80.0,70.0,75.5,高,系统默认,牛市,测试分析1,测试建议1,测试风险1
2024-12-18 16:00:00,测试股票2,SH600000,65.0,70.0,60.0,65.5,中,用户指定,熊市,测试分析2,测试建议2,测试风险2"""
    
    with open(test_csv, 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    # 导入CSV数据
    success_count, error_count = importer.import_from_csv(test_csv)
    
    if success_count == 2 and error_count == 0:
        print("✅ CSV导入成功")
    else:
        print(f"❌ CSV导入失败，成功{success_count}条，失败{error_count}条")
        return False
    
    # 验证导入的数据
    try:
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM stock_analysis_results")
        count = cursor.fetchone()[0]
        
        if count == 2:
            print("✅ CSV数据验证成功")
        else:
            print(f"❌ CSV数据验证失败，期望2条记录，实际{count}条")
            return False
            
        conn.close()
        
    except Exception as e:
        print(f"❌ CSV数据验证异常: {e}")
        return False
    
    # 清理测试文件
    os.remove(test_db)
    os.remove(test_csv)
    return True

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行数据导入工具测试")
    print("=" * 50)
    
    tests = [
        test_database_creation,
        test_data_validation,
        test_data_insertion,
        test_csv_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ 测试失败")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据导入工具工作正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    run_all_tests()
