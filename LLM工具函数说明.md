# 智能股票分析系统v2.0 - LLM工具函数说明

## 概述

`llm_stock_tools.py` 为LLM提供了直接调用的股票分析数据存储工具函数，简化了从分析到数据存储的自动化流程。

### 核心优势
- ✅ **零交互**：无需命令行交互，纯函数调用
- ✅ **自动验证**：内置数据格式和完整性验证
- ✅ **结构化返回**：统一的返回格式，便于LLM处理
- ✅ **中文支持**：支持中文字段名，降低LLM使用门槛
- ✅ **批量操作**：支持单条和批量数据处理

---

## 核心函数

### 1. save_stock_analysis() - 保存单条分析结果

```python
def save_stock_analysis(
    stock_name: str,           # 股票名称
    stock_code: str,           # 股票代码 (SZ/SH格式)
    value_score: float,        # 价值策略评分 (0-100)
    growth_score: float,       # 成长策略评分 (0-100)
    technical_score: float,    # 技术策略评分 (0-100)
    total_score: float,        # 综合总分 (0-100)
    confidence_level: str,     # 置信度等级 (高/中/低)
    weight_config: str,        # 权重配置 (用户指定/系统默认)
    market_environment: str,   # 市场环境 (牛市/熊市/震荡市/默认)
    detailed_analysis: str = "",    # 详细分析 (可选)
    investment_advice: str = "",    # 投资建议 (可选)
    risk_warnings: str = "",        # 风险提示 (可选)
    analysis_datetime: str = None,  # 分析时间 (可选)
    db_path: str = "stock_analysis.db"  # 数据库路径 (可选)
) -> Dict[str, Union[bool, int, str]]
```

**返回格式：**
```python
{
    'success': True,           # 是否成功
    'record_id': 123,         # 记录ID (成功时)
    'message': '数据保存成功...'  # 结果信息
}
# 或失败时：
{
    'success': False,
    'error': '具体错误信息',
    'message': '保存失败...'
}
```

### 2. quick_save_analysis() - 便捷保存函数

```python
def quick_save_analysis(
    analysis_result: Dict,     # 分析结果字典
    db_path: str = "stock_analysis.db"
) -> Dict
```

**支持的字段名（中英文均可）：**
- 股票名称 / stock_name
- 股票代码 / stock_code
- 价值策略评分 / value_score
- 成长策略评分 / growth_score
- 技术策略评分 / technical_score
- 综合评分 / total_score
- 置信度 / confidence_level
- 权重配置 / weight_config
- 市场环境 / market_environment
- 详细分析 / detailed_analysis
- 投资建议 / investment_advice
- 风险提示 / risk_warnings

### 3. save_batch_stock_analysis() - 批量保存

```python
def save_batch_stock_analysis(
    analysis_list: List[Dict],  # 分析结果列表
    db_path: str = "stock_analysis.db"
) -> Dict
```

**返回格式：**
```python
{
    'success': True,           # 是否全部成功
    'total_count': 10,        # 总数量
    'success_count': 9,       # 成功数量
    'failed_count': 1,        # 失败数量
    'success_ids': [1,2,3...], # 成功的记录ID列表
    'failed_items': [...],    # 失败项目详情
    'message': '批量保存完成...'
}
```

### 4. get_recent_analysis() - 获取最近记录

```python
def get_recent_analysis(
    limit: int = 10,          # 返回数量
    db_path: str = "stock_analysis.db"
) -> Dict
```

### 5. get_stock_history() - 获取股票历史

```python
def get_stock_history(
    stock_code: str,          # 股票代码
    limit: int = 10,          # 返回数量
    db_path: str = "stock_analysis.db"
) -> Dict
```

---

## LLM使用示例

### 示例1：分析完成后保存结果

```python
# LLM执行股票分析后
from llm_stock_tools import save_stock_analysis

# 保存分析结果
result = save_stock_analysis(
    stock_name="实丰文化",
    stock_code="SZ002862",
    value_score=45.0,
    growth_score=62.0,
    technical_score=48.0,
    total_score=52.3,
    confidence_level="中",
    weight_config="系统默认",
    market_environment="震荡市",
    detailed_analysis="价值策略45分：估值偏高但财务结构尚可...",
    investment_advice="谨慎考虑 - 业绩改善明显但估值仍然偏高...",
    risk_warnings="估值风险：PE过高存在回调风险..."
)

if result['success']:
    print(f"✅ 分析结果已保存，记录ID: {result['record_id']}")
else:
    print(f"❌ 保存失败: {result['error']}")
```

### 示例2：使用中文字段名（推荐）

```python
from llm_stock_tools import quick_save_analysis

# LLM生成的分析结果（中文字段名）
analysis = {
    "股票名称": "贵州茅台",
    "股票代码": "SH600519",
    "价值策略评分": 85.0,
    "成长策略评分": 75.0,
    "技术策略评分": 70.0,
    "综合评分": 78.5,
    "置信度": "高",
    "权重配置": "用户指定",
    "市场环境": "牛市",
    "详细分析": "白酒龙头企业，基本面优秀...",
    "投资建议": "强烈推荐 - 适合长期投资...",
    "风险提示": "估值风险：当前估值不便宜..."
}

result = quick_save_analysis(analysis)
print(result['message'])
```

### 示例3：批量保存多只股票

```python
from llm_stock_tools import save_batch_stock_analysis

# 批量分析结果
stocks_analysis = [
    {
        'stock_name': '股票1',
        'stock_code': 'SZ000001',
        'value_score': 70.0,
        # ... 其他字段
    },
    {
        'stock_name': '股票2', 
        'stock_code': 'SH600000',
        'value_score': 80.0,
        # ... 其他字段
    }
]

result = save_batch_stock_analysis(stocks_analysis)
print(f"批量保存: 成功{result['success_count']}条，失败{result['failed_count']}条")
```

### 示例4：查询和验证

```python
from llm_stock_tools import get_recent_analysis, get_stock_history

# 查看最近分析
recent = get_recent_analysis(limit=5)
if recent['success']:
    for record in recent['data']:
        print(f"{record['stock_name']}: {record['total_score']}分")

# 查看特定股票历史
history = get_stock_history("SZ002862", limit=3)
if history['success']:
    print(f"股票 {history['stock_code']} 有 {history['count']} 条历史记录")
```

---

## 数据验证规则

### 必需字段
- stock_name: 股票名称（非空字符串）
- stock_code: 股票代码（SZ/SH + 6位数字格式）
- value_score: 价值策略评分（0-100数字）
- growth_score: 成长策略评分（0-100数字）
- technical_score: 技术策略评分（0-100数字）
- total_score: 综合总分（0-100数字）
- confidence_level: 置信度等级（高/中/低）
- weight_config: 权重配置（用户指定/系统默认）
- market_environment: 市场环境（牛市/熊市/震荡市/默认）

### 可选字段
- detailed_analysis: 详细分析内容
- investment_advice: 投资建议
- risk_warnings: 风险提示
- analysis_datetime: 分析时间（自动生成）

### 验证错误示例
```python
# 股票代码格式错误
{'success': False, 'error': '股票代码格式错误: 002862 (应为SZ000001或SH600000格式)'}

# 评分超出范围
{'success': False, 'error': 'value_score必须在0-100之间，当前值: 150.0'}

# 置信度等级错误
{'success': False, 'error': '置信度等级必须是: 高, 中, 低'}
```

---

## 集成到LLM工作流程

### 完整工作流程示例

```python
def llm_stock_analysis_workflow(stock_name, stock_code, market_env="默认"):
    """LLM股票分析完整工作流程"""
    
    # 步骤1: LLM执行股票分析
    # (这里是模拟，实际中LLM会调用分析工具)
    analysis_result = perform_stock_analysis(stock_name, stock_code, market_env)
    
    # 步骤2: 保存分析结果
    from llm_stock_tools import quick_save_analysis
    
    save_result = quick_save_analysis(analysis_result)
    
    if save_result['success']:
        print(f"✅ 分析完成并已保存: {save_result['message']}")
        return {
            'analysis': analysis_result,
            'saved': True,
            'record_id': save_result['record_id']
        }
    else:
        print(f"❌ 分析完成但保存失败: {save_result['error']}")
        return {
            'analysis': analysis_result,
            'saved': False,
            'error': save_result['error']
        }

# 使用示例
result = llm_stock_analysis_workflow("实丰文化", "SZ002862", "震荡市")
```

---

## 错误处理

### 常见错误类型
1. **数据验证错误**：字段缺失、格式错误、数值超范围
2. **数据库错误**：连接失败、权限问题、磁盘空间不足
3. **类型错误**：参数类型不匹配

### 错误处理建议
```python
from llm_stock_tools import save_stock_analysis

try:
    result = save_stock_analysis(...)
    
    if result['success']:
        # 处理成功情况
        record_id = result['record_id']
        print(f"保存成功，ID: {record_id}")
    else:
        # 处理失败情况
        error_msg = result.get('error', '未知错误')
        print(f"保存失败: {error_msg}")
        
except Exception as e:
    # 处理异常情况
    print(f"调用异常: {e}")
```

---

## 性能优化建议

1. **批量操作**：多条数据时使用 `save_batch_stock_analysis`
2. **数据库路径**：使用绝对路径避免路径问题
3. **错误处理**：始终检查返回结果的 `success` 字段
4. **数据验证**：在调用前进行基本的数据检查

通过这些LLM工具函数，可以实现从股票分析到数据存储的完全自动化流程！
