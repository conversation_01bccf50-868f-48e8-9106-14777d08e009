# 智能股票分析系统v2.0 - 跨对话调用指南

## 快速开始

### 标准调用格式
```
按照多策略股票分析流程文档.md的完整流程，分析股票 [股票名称] [股票代码] [可选：市场环境]
```

### 立即使用
```
按照多策略股票分析流程文档.md的完整流程，分析股票 实丰文化 SZ002862
```

---

## 使用示例

### 基础分析
```
按照多策略股票分析流程文档.md的完整流程，分析股票 实丰文化 SZ002862
```

### 指定市场环境
```
按照多策略股票分析流程文档.md的完整流程，分析股票 贵州茅台 SH600519 当前是牛市
按照多策略股票分析流程文档.md的完整流程，分析股票 比亚迪 SZ002594 现在是熊市
```

### 批量分析
```
按照多策略股票分析流程文档.md的完整流程，批量分析以下股票：
1. 实丰文化 SZ002862
2. 贵州茅台 SH600519
3. 比亚迪 SZ002594

当前市场环境：震荡市
```

---

## 参数说明

### 必需参数
- **股票名称**：中文名称，如"实丰文化"
- **股票代码**：SZ/SH+6位数字，如"SZ002862"

### 可选参数
- **市场环境**：牛市/熊市/震荡市，影响权重配置

---

## 系统执行流程

### 6步分析流程
1. **权重配置** - 识别市场环境或使用默认配置
2. **数据获取** - 获取股票数据并验证
3. **三策略评分** - 价值/成长/技术各0-100分
4. **综合评分** - 基于权重的加权平均
5. **结果展示** - 控制台显示完整分析
6. **数据保存** - 自动保存到数据库

---

## 常见错误

### 股票代码格式错误
❌ `分析股票 实丰文化 002862`
✅ `分析股票 实丰文化 SZ002862`

### 缺少文档引用
❌ `分析实丰文化SZ002862`
✅ `按照多策略股票分析流程文档.md的完整流程，分析股票 实丰文化 SZ002862`

---

## Python数据保存

### 快速保存分析结果
```python
from llm_stock_tools import quick_save_analysis

# 分析结果
analysis_result = {
    "股票名称": "实丰文化",
    "股票代码": "SZ002862",
    "价值策略评分": 45.0,
    "成长策略评分": 62.0,
    "技术策略评分": 48.0,
    "综合评分": 52.3,
    "置信度": "中",
    "权重配置": "系统默认",
    "市场环境": "震荡市"
}

# 保存到数据库
result = quick_save_analysis(analysis_result)
if result['success']:
    print(f"✅ 已保存，记录ID: {result['record_id']}")
```

### 查询历史数据
```python
from llm_stock_tools import get_recent_analysis, get_stock_history

# 查看最近记录
recent = get_recent_analysis(limit=5)

# 查看股票历史
history = get_stock_history("SZ002862", limit=3)
```

---

## 最佳实践

### 推荐做法
✅ 新对话直接使用标准格式
✅ 明确指定市场环境
✅ 使用完整的文档引用

### 避免做法
❌ 省略文档引用部分
❌ 使用模糊的市场环境描述
❌ 依赖之前对话的上下文

---

## � Python数据化保存方法

### 数据库操作类
```python
import sqlite3
import pandas as pd
from datetime import datetime
import json

class StockAnalysisDB:
    """股票分析数据库管理类"""

    def __init__(self, db_path="stock_analysis.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_analysis_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                analysis_datetime TEXT NOT NULL,
                stock_name TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                value_score REAL NOT NULL,
                growth_score REAL NOT NULL,
                technical_score REAL NOT NULL,
                total_score REAL NOT NULL,
                confidence_level TEXT NOT NULL,
                weight_config TEXT NOT NULL,
                market_environment TEXT NOT NULL,
                detailed_analysis TEXT,
                investment_advice TEXT,
                risk_warnings TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_code ON stock_analysis_results(stock_code)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_datetime ON stock_analysis_results(analysis_datetime)')

        conn.commit()
        conn.close()

    def save_analysis_result(self, analysis_data):
        """保存分析结果到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO stock_analysis_results (
                analysis_datetime, stock_name, stock_code,
                value_score, growth_score, technical_score, total_score,
                confidence_level, weight_config, market_environment,
                detailed_analysis, investment_advice, risk_warnings
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            analysis_data['analysis_datetime'],
            analysis_data['stock_name'],
            analysis_data['stock_code'],
            analysis_data['value_score'],
            analysis_data['growth_score'],
            analysis_data['technical_score'],
            analysis_data['total_score'],
            analysis_data['confidence_level'],
            analysis_data['weight_config'],
            analysis_data['market_environment'],
            analysis_data.get('detailed_analysis', ''),
            analysis_data.get('investment_advice', ''),
            analysis_data.get('risk_warnings', '')
        ))

        conn.commit()
        record_id = cursor.lastrowid
        conn.close()

        return record_id

    def query_stock_history(self, stock_code, limit=10):
        """查询股票历史分析记录"""
        conn = sqlite3.connect(self.db_path)

        df = pd.read_sql_query('''
            SELECT * FROM stock_analysis_results
            WHERE stock_code = ?
            ORDER BY analysis_datetime DESC
            LIMIT ?
        ''', conn, params=(stock_code, limit))

        conn.close()
        return df

    def get_top_stocks(self, limit=20):
        """获取评分最高的股票"""
        conn = sqlite3.connect(self.db_path)

        df = pd.read_sql_query('''
            SELECT
                stock_code, stock_name, total_score,
                confidence_level, analysis_datetime,
                CASE
                    WHEN total_score >= 85 THEN '⭐⭐⭐⭐⭐ 优秀'
                    WHEN total_score >= 70 THEN '⭐⭐⭐⭐ 良好'
                    WHEN total_score >= 55 THEN '⭐⭐⭐ 一般'
                    WHEN total_score >= 40 THEN '⭐⭐ 较差'
                    ELSE '⭐ 很差'
                END as rating
            FROM stock_analysis_results s1
            WHERE analysis_datetime = (
                SELECT MAX(analysis_datetime)
                FROM stock_analysis_results s2
                WHERE s2.stock_code = s1.stock_code
            )
            ORDER BY total_score DESC
            LIMIT ?
        ''', conn, params=(limit,))

        conn.close()
        return df

    def export_to_csv(self, output_file="stock_analysis_export.csv"):
        """导出数据到CSV文件"""
        conn = sqlite3.connect(self.db_path)

        df = pd.read_sql_query('''
            SELECT * FROM stock_analysis_results
            ORDER BY analysis_datetime DESC
        ''', conn)

        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        conn.close()

        print(f"✅ 数据已导出到: {output_file}")
        return True
```

### 使用示例

#### 1. 初始化数据库
```python
# 创建数据库实例
db = StockAnalysisDB("stock_analysis.db")
print("✅ 数据库初始化完成")
```

#### 2. 保存分析结果
```python
# 准备分析数据
analysis_data = {
    'analysis_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    'stock_name': '实丰文化',
    'stock_code': 'SZ002862',
    'value_score': 45.0,
    'growth_score': 62.0,
    'technical_score': 48.0,
    'total_score': 52.3,
    'confidence_level': '中',
    'weight_config': '系统默认',
    'market_environment': '震荡市',
    'detailed_analysis': '价值策略45分：估值偏高但财务结构尚可；成长策略62分：2024年业绩显著改善；技术策略48分：短期技术面一般',
    'investment_advice': '谨慎考虑 - 业绩改善明显但估值仍然偏高，适合风险偏好较高的成长型投资者',
    'risk_warnings': '估值风险：PE=241.04存在较大回调风险；业绩风险：盈利改善的可持续性存疑'
}

# 保存到数据库
record_id = db.save_analysis_result(analysis_data)
print(f"✅ 分析结果已保存 (ID: {record_id})")
```

#### 3. 查询历史数据
```python
# 查询指定股票的历史记录
history_df = db.query_stock_history('SZ002862', limit=5)
print("📊 实丰文化历史分析记录:")
print(history_df[['analysis_datetime', 'total_score', 'confidence_level']])

# 获取评分最高的股票
top_stocks = db.get_top_stocks(limit=10)
print("🏆 评分最高的股票:")
print(top_stocks[['stock_name', 'stock_code', 'total_score', 'rating']])
```

#### 4. 数据导出
```python
# 导出所有数据到CSV
db.export_to_csv("my_stock_analysis.csv")

# 导出特定股票数据
specific_stock = db.query_stock_history('SZ002862', limit=100)
specific_stock.to_csv("SZ002862_history.csv", index=False, encoding='utf-8-sig')
print("✅ 特定股票数据已导出")
```

### 高级数据操作

#### 1. 批量数据处理
```python
def batch_save_analysis(analysis_list):
    """批量保存分析结果"""
    db = StockAnalysisDB()
    saved_ids = []

    for analysis_data in analysis_list:
        try:
            record_id = db.save_analysis_result(analysis_data)
            saved_ids.append(record_id)
            print(f"✅ {analysis_data['stock_name']} 保存成功 (ID: {record_id})")
        except Exception as e:
            print(f"❌ {analysis_data['stock_name']} 保存失败: {e}")

    return saved_ids

# 使用示例
batch_data = [
    # 多个股票的分析数据...
]
saved_ids = batch_save_analysis(batch_data)
```

#### 2. 数据分析与可视化
```python
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_portfolio_performance():
    """分析投资组合表现"""
    db = StockAnalysisDB()
    conn = sqlite3.connect(db.db_path)

    # 获取所有数据
    all_data = pd.read_sql_query("SELECT * FROM stock_analysis_results", conn)
    conn.close()

    # 绘制评分分布图
    plt.figure(figsize=(15, 10))

    # 总分分布
    plt.subplot(2, 3, 1)
    plt.hist(all_data['total_score'], bins=20, alpha=0.7, color='blue', edgecolor='black')
    plt.title('总分分布')
    plt.xlabel('总分')
    plt.ylabel('频次')

    # 三策略评分对比
    plt.subplot(2, 3, 2)
    scores = all_data[['value_score', 'growth_score', 'technical_score']]
    scores.boxplot()
    plt.title('三策略评分对比')
    plt.ylabel('评分')

    # 置信度分布
    plt.subplot(2, 3, 3)
    confidence_counts = all_data['confidence_level'].value_counts()
    plt.pie(confidence_counts.values, labels=confidence_counts.index, autopct='%1.1f%%')
    plt.title('置信度分布')

    # 市场环境分布
    plt.subplot(2, 3, 4)
    market_counts = all_data['market_environment'].value_counts()
    plt.bar(market_counts.index, market_counts.values)
    plt.title('市场环境分布')
    plt.xlabel('市场环境')
    plt.ylabel('数量')

    # 评分趋势
    plt.subplot(2, 3, 5)
    all_data['analysis_date'] = pd.to_datetime(all_data['analysis_datetime']).dt.date
    daily_avg = all_data.groupby('analysis_date')['total_score'].mean()
    plt.plot(daily_avg.index, daily_avg.values, marker='o')
    plt.title('平均评分趋势')
    plt.xlabel('日期')
    plt.ylabel('平均评分')
    plt.xticks(rotation=45)

    # 策略相关性
    plt.subplot(2, 3, 6)
    correlation_matrix = all_data[['value_score', 'growth_score', 'technical_score']].corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
    plt.title('策略评分相关性')

    plt.tight_layout()
    plt.savefig('portfolio_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    return all_data

# 执行分析
portfolio_data = analyze_portfolio_performance()
```

#### 3. 数据备份与恢复
```python
def backup_database(backup_path="backup_stock_analysis.db"):
    """备份数据库"""
    import shutil
    shutil.copy2("stock_analysis.db", backup_path)
    print(f"✅ 数据库已备份到: {backup_path}")

def restore_database(backup_path="backup_stock_analysis.db"):
    """恢复数据库"""
    import shutil
    shutil.copy2(backup_path, "stock_analysis.db")
    print(f"✅ 数据库已从备份恢复: {backup_path}")

def clean_old_data(days=30):
    """清理指定天数前的数据"""
    conn = sqlite3.connect("stock_analysis.db")
    cursor = conn.cursor()

    cursor.execute('''
        DELETE FROM stock_analysis_results
        WHERE created_at < datetime('now', '-{} days')
    '''.format(days))

    deleted_count = cursor.rowcount
    conn.commit()
    conn.close()

    print(f"✅ 已清理 {deleted_count} 条 {days} 天前的数据")
    return deleted_count
```

#### 4. 投资组合优化
```python
def optimize_portfolio(target_score=70, max_stocks=10):
    """基于评分优化投资组合"""
    db = StockAnalysisDB()

    # 获取最新的高评分股票
    top_stocks = db.get_top_stocks(limit=50)

    # 筛选高评分股票
    qualified_stocks = top_stocks[top_stocks['total_score'] >= target_score]

    if len(qualified_stocks) == 0:
        print(f"⚠️ 没有找到评分 >= {target_score} 的股票")
        return None

    # 选择前N只股票
    selected_stocks = qualified_stocks.head(max_stocks)

    # 计算权重（基于评分）
    total_score = selected_stocks['total_score'].sum()
    selected_stocks['weight'] = selected_stocks['total_score'] / total_score

    print(f"🎯 优化投资组合（目标评分>={target_score}）:")
    print("=" * 60)
    for _, stock in selected_stocks.iterrows():
        print(f"{stock['stock_name']} ({stock['stock_code']}): "
              f"{stock['total_score']:.1f}分 - 权重{stock['weight']:.1%}")

    return selected_stocks

# 使用示例
portfolio = optimize_portfolio(target_score=70, max_stocks=8)
```

#### 5. 风险分析
```python
def analyze_portfolio_risk():
    """分析投资组合风险"""
    db = StockAnalysisDB()
    conn = sqlite3.connect(db.db_path)

    # 获取最新数据
    latest_data = pd.read_sql_query('''
        SELECT s1.* FROM stock_analysis_results s1
        WHERE s1.analysis_datetime = (
            SELECT MAX(s2.analysis_datetime)
            FROM stock_analysis_results s2
            WHERE s2.stock_code = s1.stock_code
        )
    ''', conn)
    conn.close()

    # 风险分析
    risk_analysis = {
        'high_risk_stocks': latest_data[latest_data['confidence_level'] == '低'],
        'volatile_stocks': latest_data[latest_data['technical_score'] < 40],
        'overvalued_stocks': latest_data[latest_data['value_score'] < 30],
        'growth_concerns': latest_data[latest_data['growth_score'] < 40]
    }

    print("🚨 投资组合风险分析:")
    print("=" * 50)

    for risk_type, stocks in risk_analysis.items():
        if len(stocks) > 0:
            print(f"\n{risk_type.replace('_', ' ').title()}: {len(stocks)}只")
            for _, stock in stocks.iterrows():
                print(f"  - {stock['stock_name']} ({stock['stock_code']}): "
                      f"{stock['total_score']:.1f}分")

    return risk_analysis

# 执行风险分析
risk_report = analyze_portfolio_risk()
```

---

## �📚 相关文档

### 核心文档
- **主文档**：`多策略股票分析流程文档.md`
- **调用指南**：`智能股票分析系统调用指南.md`
- **数据库脚本**：`database_init.py`

### 示例文档
- **调用示例**：`test/标准化调用示例.md`
- **分析报告**：`test/SZ002862_实丰文化_智能分析_20241218.md`

---

## 🎯 快速开始

### 立即体验
复制以下调用格式，替换股票信息即可：

```
按照多策略股票分析流程文档.md的完整流程，分析股票 实丰文化 SZ002862
```

### 自定义调用
```
按照多策略股票分析流程文档.md的完整流程，分析股票 [您的股票名称] [您的股票代码] [可选：当前是牛市/熊市/震荡市]
```

通过这个跨对话标准调用格式，您可以在任何新的对话中稳定、可靠地使用智能股票分析系统v2.0！
